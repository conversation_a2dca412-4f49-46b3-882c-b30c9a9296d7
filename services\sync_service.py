import os
import shutil
import logging
from datetime import datetime, timedelta
from models import db, Artifact, SyncRecord, Environment
from services.artifact_service import ArtifactService
from services.notification_service import NotificationService
from services.cmdb_service import CMDBService
from services.package_validator import PackageValidator
from config import Config

class SyncService:
    """同步服务"""
    
    def __init__(self):
        self.config = Config()
        self.artifact_service = ArtifactService()
        self.notification_service = NotificationService()
        self.cmdb_service = CMDBService()
        self.package_validator = PackageValidator()
        self.logger = logging.getLogger(__name__)
        self.sync_logger = logging.getLogger('sync')
        self.error_logger = logging.getLogger('error')
    
    def check_and_sync_new_versions(self):
        """检查并同步新版本"""
        try:
            self.sync_logger.info("开始检查新版本")

            # 获取新版本信息
            new_versions = self.artifact_service.check_new_versions()
            self.sync_logger.info(f"发现 {len(new_versions)} 个版本信息")

            # 只处理指定状态的制品
            valid_statuses = self.config.SYNC_CONFIG['valid_statuses']
            filtered_versions = [v for v in new_versions if v.get('status') in valid_statuses]
            self.sync_logger.info(f"过滤后有效版本: {len(filtered_versions)} 个（状态: {valid_statuses}）")

            sync_count = 0
            for version_data in filtered_versions:
                # 检查是否已存在
                existing = Artifact.query.filter_by(
                    artifact_id=version_data['artifact_id'],
                    version=version_data['version']
                ).first()

                if not existing:
                    self.sync_logger.info(f"发现新版本: {version_data['artifact_id']} v{version_data['version']} ({version_data['status']})")

                    # 创建新的制品记录
                    artifact = self.artifact_service.create_artifact(version_data)

                    # 注册到CMDB
                    self.cmdb_service.register_artifact(artifact.to_dict())

                    # 根据状态决定同步目标
                    target_env = self._determine_target_env(artifact)
                    self.sync_logger.info(f"准备同步制品: {artifact.artifact_id} v{artifact.version} -> {target_env}")

                    # 检查CMDB中是否已存在成功的同步记录
                    if not self.cmdb_service.check_artifact_exists_in_env(artifact.artifact_id, artifact.version, target_env):
                        try:
                            self._sync_artifact(artifact, target_env)
                            sync_count += 1
                        except Exception as e:
                            self.error_logger.error(f"自动同步失败: {artifact.artifact_id} -> {target_env}, 错误: {str(e)}")
                    else:
                        self.sync_logger.info(f"制品已存在于目标环境，跳过同步: {artifact.artifact_id} v{artifact.version} -> {target_env}")
                else:
                    self.sync_logger.debug(f"版本已存在，跳过: {version_data['artifact_id']} v{version_data['version']}")

            self.sync_logger.info(f"检查新版本完成，成功同步 {sync_count} 个制品")

        except Exception as e:
            self.error_logger.error(f"检查新版本失败: {str(e)}")
            self.notification_service.send_error_notification(f"检查新版本失败: {str(e)}")
    
    def manual_sync(self, artifact_id, target_env):
        """手动同步"""
        self.sync_logger.info(f"开始手动同步: {artifact_id} -> {target_env}")
        
        artifact = Artifact.query.filter_by(artifact_id=artifact_id).first()
        if not artifact:
            error_msg = f"制品不存在: {artifact_id}"
            self.error_logger.error(error_msg)
            raise Exception(error_msg)
        
        return self._sync_artifact(artifact, target_env, sync_type='manual')
    
    def sync_by_date(self, sync_date, target_env):
        """按日期同步"""
        start_date = sync_date
        end_date = sync_date + timedelta(days=1)
        
        self.sync_logger.info(f"开始按日期同步: {sync_date.strftime('%Y-%m-%d')} -> {target_env}")
        
        artifacts = Artifact.query.filter(
            Artifact.created_at >= start_date,
            Artifact.created_at < end_date,
            Artifact.status.in_(['预发布', '生产'])
        ).all()
        
        self.sync_logger.info(f"找到 {len(artifacts)} 个符合条件的制品")
        
        results = []
        success_count = 0
        failed_count = 0
        
        for artifact in artifacts:
            try:
                self.sync_logger.info(f"同步制品: {artifact.artifact_id} v{artifact.version}")
                result = self._sync_artifact(artifact, target_env, sync_type='manual')
                results.append(result)
                success_count += 1
            except Exception as e:
                error_msg = str(e)
                self.error_logger.error(f"按日期同步失败: {artifact.artifact_id} -> {target_env}, 错误: {error_msg}")
                results.append({
                    'artifact_id': artifact.artifact_id,
                    'status': 'failed',
                    'error': error_msg
                })
                failed_count += 1
        
        self.sync_logger.info(f"按日期同步完成: 成功 {success_count} 个，失败 {failed_count} 个")
        return results

    def sync_by_date_unified(self, sync_date):
        """指定时间同步制品（统一接口格式）"""
        start_date = sync_date
        end_date = sync_date + timedelta(days=1)

        self.sync_logger.info(f"开始指定时间同步: {sync_date.strftime('%Y-%m-%d')}")

        # 只扫描指定状态的制品
        valid_statuses = self.config.SYNC_CONFIG['valid_statuses']
        artifacts = Artifact.query.filter(
            Artifact.created_at >= start_date,
            Artifact.created_at < end_date,
            Artifact.status.in_(valid_statuses)
        ).all()

        self.sync_logger.info(f"找到 {len(artifacts)} 个符合条件的制品")

        results = []
        for artifact in artifacts:
            try:
                # 根据状态确定目标环境
                target_env = self._determine_target_env(artifact)
                self.sync_logger.info(f"同步制品: {artifact.artifact_id} v{artifact.version} -> {target_env}")

                sync_result = self._sync_artifact(artifact, target_env, sync_type='manual')

                results.append({
                    "artifactName": artifact.name,
                    "artifactId": artifact.artifact_id,
                    "versionName": artifact.version,
                    "versionId": artifact.version_id,
                    "memo": artifact.description or "",
                    "date": artifact.created_at.strftime('%Y-%m-%d') if artifact.created_at else "",
                    "status": "success",
                    "targetEnv": target_env
                })
            except Exception as e:
                error_msg = str(e)
                self.error_logger.error(f"指定时间同步失败: {artifact.artifact_id}, 错误: {error_msg}")
                results.append({
                    "artifactName": artifact.name,
                    "artifactId": artifact.artifact_id,
                    "versionName": artifact.version,
                    "versionId": artifact.version_id,
                    "memo": artifact.description or "",
                    "date": artifact.created_at.strftime('%Y-%m-%d') if artifact.created_at else "",
                    "status": "failed",
                    "error": error_msg
                })

        self.sync_logger.info(f"指定时间同步完成: 处理 {len(results)} 个制品")
        return results

    def sync_by_artifact_name(self, artifact_name):
        """指定程序包名称同步制品"""
        self.sync_logger.info(f"开始程序包名称同步: {artifact_name}")

        # 只扫描指定状态的制品
        valid_statuses = self.config.SYNC_CONFIG['valid_statuses']
        artifacts = Artifact.query.filter(
            Artifact.name == artifact_name,
            Artifact.status.in_(valid_statuses)
        ).all()

        self.sync_logger.info(f"找到 {len(artifacts)} 个符合条件的制品")

        results = []
        for artifact in artifacts:
            try:
                # 根据状态确定目标环境
                target_env = self._determine_target_env(artifact)
                self.sync_logger.info(f"同步制品: {artifact.artifact_id} v{artifact.version} -> {target_env}")

                sync_result = self._sync_artifact(artifact, target_env, sync_type='manual')

                results.append({
                    "artifactName": artifact.name,
                    "artifactId": artifact.artifact_id,
                    "versionName": artifact.version,
                    "versionId": artifact.version_id,
                    "memo": artifact.description or "",
                    "date": artifact.created_at.strftime('%Y-%m-%d') if artifact.created_at else "",
                    "status": "success",
                    "targetEnv": target_env
                })
            except Exception as e:
                error_msg = str(e)
                self.error_logger.error(f"程序包同步失败: {artifact.artifact_id}, 错误: {error_msg}")
                results.append({
                    "artifactName": artifact.name,
                    "artifactId": artifact.artifact_id,
                    "versionName": artifact.version,
                    "versionId": artifact.version_id,
                    "memo": artifact.description or "",
                    "date": artifact.created_at.strftime('%Y-%m-%d') if artifact.created_at else "",
                    "status": "failed",
                    "error": error_msg
                })

        self.sync_logger.info(f"程序包同步完成: 处理 {len(results)} 个制品")
        return results

    def get_sync_records_by_date(self, query_date):
        """指定时间查询同步记录"""
        start_date = query_date
        end_date = query_date + timedelta(days=1)

        self.sync_logger.info(f"开始查询同步记录: {query_date.strftime('%Y-%m-%d')}")

        records = SyncRecord.query.filter(
            SyncRecord.created_at >= start_date,
            SyncRecord.created_at < end_date
        ).order_by(SyncRecord.created_at.desc()).all()

        self.sync_logger.info(f"找到 {len(records)} 条同步记录")

        results = []
        for record in records:
            results.append({
                "syncId": record.id,
                "artifactName": record.artifact_name,
                "artifactId": record.artifact_id,
                "versionName": record.version,
                "sourceEnv": record.source_env,
                "targetEnv": record.target_env,
                "status": record.status,
                "syncType": record.sync_type,
                "errorMessage": record.error_message or "",
                "retryCount": record.retry_count,
                "createdAt": record.created_at.strftime('%Y-%m-%d %H:%M:%S') if record.created_at else "",
                "completedAt": record.completed_at.strftime('%Y-%m-%d %H:%M:%S') if record.completed_at else ""
            })

        return results
    
    def _sync_artifact(self, artifact, target_env, sync_type='auto'):
        """同步制品"""
        sync_start_time = datetime.utcnow()
        
        # 记录同步开始
        self.sync_logger.info(f"[{sync_type.upper()}] 开始同步制品: {artifact.artifact_id} v{artifact.version} ({artifact.name}) -> {target_env}")
        
        sync_record = SyncRecord(
            artifact_id=artifact.artifact_id,
            artifact_name=artifact.name,
            version=artifact.version,
            source_env=artifact.environment,
            target_env=target_env,
            status='in_progress',
            sync_type=sync_type
        )
        db.session.add(sync_record)
        db.session.commit()
        
        try:
            # 1. 下载制品
            download_dir = self.config.SYNC_CONFIG.get('download_dir', '/tmp/artifacts')
            os.makedirs(download_dir, exist_ok=True)

            # 为目标环境添加后缀
            env_config = self.config.ENVIRONMENTS.get(target_env, {})
            env_suffix = env_config.get('artifact_suffix', '')
            local_filename = f"{artifact.artifact_id}_{artifact.version}{env_suffix}"
            local_path = os.path.join(download_dir, local_filename)

            self.sync_logger.info(f"[{sync_type.upper()}] 开始下载制品: {artifact.download_url} -> {local_path}")

            download_start = datetime.utcnow()
            self.artifact_service.download_artifact(artifact.download_url, local_path)
            download_time = (datetime.utcnow() - download_start).total_seconds()

            self.sync_logger.info(f"[{sync_type.upper()}] 下载完成，耗时: {download_time:.2f}秒, 文件大小: {artifact.file_size} bytes")

            # 2. 验证文件校验和
            if artifact.checksum:
                self.sync_logger.info(f"[{sync_type.upper()}] 开始校验文件完整性: {artifact.checksum}")
                verify_start = datetime.utcnow()

                if not self.artifact_service.verify_checksum(local_path, artifact.checksum):
                    error_msg = "文件校验和验证失败"
                    self.error_logger.error(f"[{sync_type.upper()}] {error_msg}: {artifact.artifact_id}")
                    raise Exception(error_msg)

                verify_time = (datetime.utcnow() - verify_start).total_seconds()
                self.sync_logger.info(f"[{sync_type.upper()}] 文件校验和验证成功，耗时: {verify_time:.2f}秒")
            else:
                self.sync_logger.warning(f"[{sync_type.upper()}] 未提供校验和，跳过校验和验证")

            # 3. 包完整性测试（解压测试）
            if self.config.SYNC_CONFIG.get('test_extract', True):
                self.sync_logger.info(f"[{sync_type.upper()}] 开始包完整性测试（解压测试）")
                package_test_start = datetime.utcnow()

                if not self.package_validator.validate_package(local_path):
                    error_msg = "包完整性测试失败（解压测试失败）"
                    self.error_logger.error(f"[{sync_type.upper()}] {error_msg}: {artifact.artifact_id}")
                    raise Exception(error_msg)

                package_test_time = (datetime.utcnow() - package_test_start).total_seconds()
                self.sync_logger.info(f"[{sync_type.upper()}] 包完整性测试成功，耗时: {package_test_time:.2f}秒")

                # 获取包信息
                package_info = self.package_validator.get_package_info(local_path)
                if package_info:
                    self.sync_logger.info(f"[{sync_type.upper()}] 包信息: 格式={package_info.get('file_extension')}, "
                                        f"内容数量={package_info.get('content_count', 'unknown')}")
            else:
                self.sync_logger.info(f"[{sync_type.upper()}] 跳过包完整性测试（配置禁用）")
            
            # 3. 上传到目标环境
            self.sync_logger.info(f"[{sync_type.upper()}] 开始上传到目标环境: {target_env}")
            upload_start = datetime.utcnow()
            
            self._upload_to_target(local_path, target_env, artifact)
            
            upload_time = (datetime.utcnow() - upload_start).total_seconds()
            self.sync_logger.info(f"[{sync_type.upper()}] 上传完成，耗时: {upload_time:.2f}秒")
            
            # 4. 更新同步记录
            sync_record.status = 'success'
            sync_record.completed_at = datetime.utcnow()
            db.session.commit()

            # 5. 同步记录到CMDB
            cmdb_sync_data = {
                'artifact_id': artifact.artifact_id,
                'artifact_name': artifact.name,
                'version': artifact.version,
                'source_env': artifact.environment,
                'target_env': target_env,
                'status': 'success',
                'sync_type': sync_type,
                'error_message': None,
                'retry_count': sync_record.retry_count,
                'created_at': sync_record.created_at.isoformat() if sync_record.created_at else None,
                'completed_at': sync_record.completed_at.isoformat() if sync_record.completed_at else None
            }

            cmdb_record = self.cmdb_service.create_sync_record(cmdb_sync_data)
            if cmdb_record:
                self.sync_logger.info(f"[{sync_type.upper()}] CMDB同步记录创建成功: {cmdb_record.get('id', 'unknown')}")

            # 6. 清理临时文件
            if os.path.exists(local_path):
                os.remove(local_path)
                self.sync_logger.info(f"[{sync_type.upper()}] 清理临时文件: {local_path}")

            total_time = (datetime.utcnow() - sync_start_time).total_seconds()

            # 记录成功日志
            success_msg = (f"[{sync_type.upper()}] 同步成功: {artifact.artifact_id} v{artifact.version} "
                          f"({artifact.name}) -> {target_env}, 总耗时: {total_time:.2f}秒, "
                          f"记录ID: {sync_record.id}")
            self.sync_logger.info(success_msg)

            # 发送成功通知
            self.notification_service.send_sync_success_notification(artifact, target_env)
            
            return {
                'status': 'success',
                'artifact_id': artifact.artifact_id,
                'target_env': target_env,
                'sync_record_id': sync_record.id,
                'duration': total_time
            }
            
        except Exception as e:
            # 更新失败记录
            sync_record.status = 'failed'
            sync_record.error_message = str(e)
            sync_record.retry_count += 1
            sync_record.completed_at = datetime.utcnow()
            db.session.commit()

            # 同步失败记录到CMDB
            cmdb_sync_data = {
                'artifact_id': artifact.artifact_id,
                'artifact_name': artifact.name,
                'version': artifact.version,
                'source_env': artifact.environment,
                'target_env': target_env,
                'status': 'failed',
                'sync_type': sync_type,
                'error_message': str(e),
                'retry_count': sync_record.retry_count,
                'created_at': sync_record.created_at.isoformat() if sync_record.created_at else None,
                'completed_at': sync_record.completed_at.isoformat() if sync_record.completed_at else None
            }

            cmdb_record = self.cmdb_service.create_sync_record(cmdb_sync_data)
            if cmdb_record:
                self.sync_logger.info(f"[{sync_type.upper()}] CMDB失败记录创建成功: {cmdb_record.get('id', 'unknown')}")

            total_time = (datetime.utcnow() - sync_start_time).total_seconds()

            # 记录失败日志
            error_msg = (f"[{sync_type.upper()}] 同步失败: {artifact.artifact_id} v{artifact.version} "
                        f"({artifact.name}) -> {target_env}, 错误: {str(e)}, "
                        f"重试次数: {sync_record.retry_count}, 耗时: {total_time:.2f}秒, "
                        f"记录ID: {sync_record.id}")
            self.error_logger.error(error_msg)

            # 重试逻辑
            if sync_record.retry_count < self.config.SYNC_CONFIG['max_retries']:
                self.sync_logger.info(f"[{sync_type.upper()}] 将进行重试: {artifact.artifact_id}, "
                                    f"当前重试次数: {sync_record.retry_count}/{self.config.SYNC_CONFIG['max_retries']}")
            else:
                # 发送失败通知
                self.sync_logger.error(f"[{sync_type.upper()}] 达到最大重试次数，发送失败通知: {artifact.artifact_id}")
                self.notification_service.send_sync_failure_notification(
                    artifact, target_env, str(e)
                )

            raise e
    
    def _determine_target_env(self, artifact):
        """根据制品状态确定目标环境"""
        if artifact.status == '预发布':
            if 'UAT' in artifact.name:
                target_env = 'TEST_EXTERNAL'
            else:
                target_env = 'TEST_INTERNAL'
        elif artifact.status == '生产':
            target_env = 'PROD_INTERNAL'
        else:
            target_env = 'TEST_INTERNAL'
        
        self.sync_logger.info(f"根据制品状态确定目标环境: {artifact.status} -> {target_env}")
        return target_env
    
    def _upload_to_target(self, local_path, target_env, artifact):
        """上传到目标环境（模拟）"""
        env_config = self.config.ENVIRONMENTS.get(target_env)
        if not env_config:
            error_msg = f"未知的目标环境: {target_env}"
            self.error_logger.error(error_msg)
            raise Exception(error_msg)
        
        self.sync_logger.info(f"上传到目标环境: {env_config['name']} ({env_config['url']})")
        
        # 模拟上传过程
        # 实际实现中这里会调用目标环境的API
        import time
        time.sleep(1)  # 模拟上传时间
        
        # 模拟上传失败的情况（10%概率）
        import random
        if random.random() < 0.1:
            error_msg = "模拟上传失败"
            self.sync_logger.error(f"上传失败: {error_msg}")
            raise Exception(error_msg)
        
        self.sync_logger.info(f"上传成功到: {target_env}")
        return True
