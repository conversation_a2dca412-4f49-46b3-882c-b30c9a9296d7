#!/usr/bin/env python3
"""
测试增强功能的脚本
"""

import os
import sys
import tempfile
import tarfile
import zipfile
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from services.cmdb_service import CMDBService
from services.package_validator import PackageValidator

def test_config():
    """测试配置"""
    print("=== 测试配置 ===")
    config = Config()
    
    print("环境配置:")
    for env_code, env_config in config.ENVIRONMENTS.items():
        print(f"  {env_code}: {env_config['name']} (后缀: {env_config.get('artifact_suffix', 'N/A')})")
    
    print(f"\nCMDB配置:")
    print(f"  基础URL: {config.CMDB_CONFIG['base_url']}")
    print(f"  项目ID: {config.CMDB_CONFIG['project_id']}")
    
    print(f"\n同步配置:")
    print(f"  有效状态: {config.SYNC_CONFIG['valid_statuses']}")
    print(f"  下载目录: {config.SYNC_CONFIG['download_dir']}")
    print(f"  测试解压: {config.SYNC_CONFIG['test_extract']}")
    print(f"  支持格式: {config.SYNC_CONFIG['supported_formats']}")
    
    print("✅ 配置测试完成\n")

def test_package_validator():
    """测试包验证器"""
    print("=== 测试包验证器 ===")
    
    validator = PackageValidator()
    
    # 创建测试文件
    test_files = []
    
    # 创建tar.gz测试文件
    with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as f:
        tar_path = f.name
        test_files.append(tar_path)
        
        with tarfile.open(tar_path, 'w:gz') as tar:
            # 添加一个测试文件
            info = tarfile.TarInfo(name="test.txt")
            info.size = 11
            tar.addfile(info, fileobj=tempfile.BytesIO(b"Hello World"))
    
    # 创建zip测试文件
    with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as f:
        zip_path = f.name
        test_files.append(zip_path)
        
        with zipfile.ZipFile(zip_path, 'w') as zip_file:
            zip_file.writestr("test.txt", "Hello World")
    
    # 测试验证功能
    for test_file in test_files:
        print(f"测试文件: {test_file}")
        
        # 获取包信息
        info = validator.get_package_info(test_file)
        if info:
            print(f"  文件信息: {info['file_extension']}, 大小: {info['file_size']} bytes")
            print(f"  支持格式: {info['is_supported']}")
            if 'content_count' in info:
                print(f"  内容数量: {info['content_count']}")
        
        # 验证包完整性
        is_valid = validator.validate_package(test_file)
        print(f"  验证结果: {'✅ 通过' if is_valid else '❌ 失败'}")
        print()
    
    # 清理测试文件
    for test_file in test_files:
        try:
            os.unlink(test_file)
        except:
            pass
    
    print("✅ 包验证器测试完成\n")

def test_cmdb_service():
    """测试CMDB服务"""
    print("=== 测试CMDB服务 ===")
    
    try:
        cmdb = CMDBService()
        print(f"CMDB服务初始化成功")
        print(f"  基础URL: {cmdb.base_url}")
        print(f"  项目ID: {cmdb.project_id}")
        print(f"  端点配置: {list(cmdb.endpoints.keys())}")
        
        # 测试数据
        test_artifact_data = {
            'artifact_id': 'test_artifact_001',
            'name': 'test_artifact',
            'version': '1.0.0',
            'version_id': 'v100',
            'status': '预发布',
            'description': '测试制品',
            'file_size': 1024,
            'checksum': 'test_checksum',
            'download_url': 'http://example.com/test',
            'environment': 'TEST',
            'created_at': datetime.now().isoformat()
        }
        
        test_sync_data = {
            'artifact_id': 'test_artifact_001',
            'artifact_name': 'test_artifact',
            'version': '1.0.0',
            'source_env': 'TEST',
            'target_env': 'PROD_INTERNAL',
            'status': 'success',
            'sync_type': 'manual',
            'error_message': None,
            'retry_count': 0,
            'created_at': datetime.now().isoformat(),
            'completed_at': datetime.now().isoformat()
        }
        
        print("\n模拟CMDB操作:")
        print(f"  制品注册数据: {test_artifact_data['artifact_id']}")
        print(f"  同步记录数据: {test_sync_data['artifact_id']} -> {test_sync_data['target_env']}")
        
        # 注意：这里不实际调用CMDB API，只是测试数据结构
        print("  (实际API调用需要有效的CMDB服务)")
        
    except Exception as e:
        print(f"❌ CMDB服务测试失败: {e}")
        return
    
    print("✅ CMDB服务测试完成\n")

def test_environment_suffix():
    """测试环境后缀功能"""
    print("=== 测试环境后缀功能 ===")
    
    config = Config()
    
    # 模拟制品文件名生成
    artifact_id = "percat_UAT_001"
    version = "1.2.3"
    
    for env_code, env_config in config.ENVIRONMENTS.items():
        suffix = env_config.get('artifact_suffix', '')
        filename = f"{artifact_id}_{version}{suffix}"
        print(f"  {env_code}: {filename}")
    
    print("✅ 环境后缀测试完成\n")

def test_status_filtering():
    """测试状态过滤功能"""
    print("=== 测试状态过滤功能 ===")
    
    config = Config()
    valid_statuses = config.SYNC_CONFIG['valid_statuses']
    
    print(f"有效状态: {valid_statuses}")
    
    # 模拟制品状态测试
    test_statuses = ['预发布', '生产', '开发', '测试', '废弃']
    
    for status in test_statuses:
        is_valid = status in valid_statuses
        print(f"  {status}: {'✅ 处理' if is_valid else '❌ 跳过'}")
    
    print("✅ 状态过滤测试完成\n")

def create_test_package():
    """创建测试包文件"""
    print("=== 创建测试包文件 ===")
    
    config = Config()
    download_dir = config.SYNC_CONFIG.get('download_dir', '/tmp/artifacts')
    
    # 确保目录存在
    os.makedirs(download_dir, exist_ok=True)
    
    # 创建测试tar.gz文件
    test_tar_path = os.path.join(download_dir, 'test_package.tar.gz')
    
    with tarfile.open(test_tar_path, 'w:gz') as tar:
        # 添加一些测试文件
        for i in range(3):
            info = tarfile.TarInfo(name=f"file_{i}.txt")
            content = f"This is test file {i}\n".encode()
            info.size = len(content)
            tar.addfile(info, fileobj=tempfile.BytesIO(content))
    
    print(f"创建测试包: {test_tar_path}")
    
    # 验证包
    validator = PackageValidator()
    is_valid = validator.validate_package(test_tar_path)
    print(f"包验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    # 获取包信息
    info = validator.get_package_info(test_tar_path)
    if info:
        print(f"包信息:")
        print(f"  文件大小: {info['file_size']} bytes")
        print(f"  文件格式: {info['file_extension']}")
        print(f"  内容数量: {info.get('content_count', 'unknown')}")
    
    print("✅ 测试包创建完成\n")
    return test_tar_path

def main():
    """主函数"""
    print("🧪 摆渡机系统增强功能测试")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_config()
        test_package_validator()
        test_cmdb_service()
        test_environment_suffix()
        test_status_filtering()
        
        # 创建测试包
        test_package_path = create_test_package()
        
        print("🎉 所有测试完成!")
        print("=" * 50)
        print("新增功能说明:")
        print("1. ✅ 环境后缀配置 - 每个环境有不同的制品名称后缀")
        print("2. ✅ 状态过滤 - 只扫描'预发布'和'生产'状态的制品")
        print("3. ✅ 包完整性测试 - 下载后进行解压测试验证")
        print("4. ✅ CMDB集成 - 同步记录自动提交到CMDB模型")
        print("5. ✅ 增强的下载验证 - 校验和 + 解压测试双重验证")
        
        print("\n📝 使用建议:")
        print("1. 启动应用前确保CMDB服务可用")
        print("2. 检查下载目录权限和空间")
        print("3. 根据需要调整支持的压缩格式")
        print("4. 监控同步日志和CMDB记录")
        
        # 清理测试文件
        if os.path.exists(test_package_path):
            os.unlink(test_package_path)
            print(f"\n🧹 清理测试文件: {test_package_path}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
