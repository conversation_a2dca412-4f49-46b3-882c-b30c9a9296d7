version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=***********************************/ferry_system
      - ARTIFACT_REPO_URL=http://116.204.72.136/next/contract-center
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs
      - ./artifacts:/tmp/artifacts

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=ferry_system
      - POSTGRES_USER=ferry
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data: