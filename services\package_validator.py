import os
import tarfile
import zipfile
import tempfile
import shutil
import subprocess
import logging
from pathlib import Path
from config import Config

class PackageValidator:
    """包完整性验证服务"""
    
    def __init__(self):
        self.config = Config()
        self.sync_config = self.config.SYNC_CONFIG
        self.supported_formats = self.sync_config['supported_formats']
        self.logger = logging.getLogger(__name__)
        self.sync_logger = logging.getLogger('sync')
        self.error_logger = logging.getLogger('error')
    
    def validate_package(self, file_path):
        """验证包的完整性"""
        try:
            self.sync_logger.info(f"开始验证包完整性: {file_path}")
            
            if not os.path.exists(file_path):
                raise Exception(f"文件不存在: {file_path}")
            
            # 获取文件扩展名
            file_ext = self._get_file_extension(file_path)
            
            if not self._is_supported_format(file_ext):
                self.sync_logger.warning(f"不支持的文件格式: {file_ext}, 跳过解压测试")
                return True  # 对于不支持的格式，只要文件存在就认为有效
            
            # 创建临时目录进行解压测试
            with tempfile.TemporaryDirectory() as temp_dir:
                self.sync_logger.info(f"使用临时目录进行解压测试: {temp_dir}")
                
                # 根据文件类型进行解压测试
                if file_ext in ['.tar.gz', '.tar.bz2', '.tar.xz', '.tar']:
                    return self._validate_tar_package(file_path, temp_dir)
                elif file_ext == '.zip':
                    return self._validate_zip_package(file_path, temp_dir)
                else:
                    # 尝试使用tar命令测试
                    return self._validate_with_tar_command(file_path)
            
        except Exception as e:
            error_msg = f"包完整性验证失败: {file_path}, 错误: {str(e)}"
            self.error_logger.error(error_msg)
            return False
    
    def _get_file_extension(self, file_path):
        """获取文件扩展名"""
        path = Path(file_path)
        
        # 处理双扩展名，如 .tar.gz
        if path.suffix == '.gz' and path.stem.endswith('.tar'):
            return '.tar.gz'
        elif path.suffix == '.bz2' and path.stem.endswith('.tar'):
            return '.tar.bz2'
        elif path.suffix == '.xz' and path.stem.endswith('.tar'):
            return '.tar.xz'
        else:
            return path.suffix.lower()
    
    def _is_supported_format(self, file_ext):
        """检查是否为支持的格式"""
        return file_ext in self.supported_formats
    
    def _validate_tar_package(self, file_path, temp_dir):
        """验证tar格式的包"""
        try:
            self.sync_logger.info(f"验证tar包: {file_path}")
            
            # 使用Python的tarfile模块测试
            with tarfile.open(file_path, 'r:*') as tar:
                # 检查tar文件是否可以正常打开
                members = tar.getmembers()
                self.sync_logger.info(f"tar包包含 {len(members)} 个文件/目录")
                
                # 尝试解压几个文件进行测试
                test_count = min(5, len(members))  # 最多测试5个文件
                for i, member in enumerate(members[:test_count]):
                    if member.isfile():
                        try:
                            tar.extract(member, temp_dir)
                            extracted_path = os.path.join(temp_dir, member.name)
                            if os.path.exists(extracted_path):
                                self.sync_logger.debug(f"成功解压测试文件: {member.name}")
                            else:
                                raise Exception(f"解压文件不存在: {member.name}")
                        except Exception as e:
                            raise Exception(f"解压文件失败: {member.name}, 错误: {str(e)}")
            
            self.sync_logger.info(f"tar包验证成功: {file_path}")
            return True
            
        except Exception as e:
            self.error_logger.error(f"tar包验证失败: {file_path}, 错误: {str(e)}")
            return False
    
    def _validate_zip_package(self, file_path, temp_dir):
        """验证zip格式的包"""
        try:
            self.sync_logger.info(f"验证zip包: {file_path}")
            
            # 使用Python的zipfile模块测试
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # 检查zip文件是否可以正常打开
                file_list = zip_file.namelist()
                self.sync_logger.info(f"zip包包含 {len(file_list)} 个文件/目录")
                
                # 测试zip文件完整性
                bad_file = zip_file.testzip()
                if bad_file:
                    raise Exception(f"zip文件损坏，损坏的文件: {bad_file}")
                
                # 尝试解压几个文件进行测试
                test_count = min(5, len(file_list))  # 最多测试5个文件
                for i, file_name in enumerate(file_list[:test_count]):
                    if not file_name.endswith('/'):  # 跳过目录
                        try:
                            zip_file.extract(file_name, temp_dir)
                            extracted_path = os.path.join(temp_dir, file_name)
                            if os.path.exists(extracted_path):
                                self.sync_logger.debug(f"成功解压测试文件: {file_name}")
                            else:
                                raise Exception(f"解压文件不存在: {file_name}")
                        except Exception as e:
                            raise Exception(f"解压文件失败: {file_name}, 错误: {str(e)}")
            
            self.sync_logger.info(f"zip包验证成功: {file_path}")
            return True
            
        except Exception as e:
            self.error_logger.error(f"zip包验证失败: {file_path}, 错误: {str(e)}")
            return False
    
    def _validate_with_tar_command(self, file_path):
        """使用tar命令验证包完整性"""
        try:
            self.sync_logger.info(f"使用tar命令验证包: {file_path}")
            
            # 使用tar -tf命令测试文件列表
            result = subprocess.run(
                ['tar', '-tf', file_path],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                file_count = len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
                self.sync_logger.info(f"tar命令验证成功: {file_path}, 包含 {file_count} 个文件")
                return True
            else:
                error_msg = f"tar命令验证失败: {file_path}, 错误: {result.stderr}"
                self.error_logger.error(error_msg)
                return False
                
        except subprocess.TimeoutExpired:
            self.error_logger.error(f"tar命令验证超时: {file_path}")
            return False
        except FileNotFoundError:
            self.sync_logger.warning("系统未安装tar命令，跳过命令行验证")
            return True  # 如果没有tar命令，认为验证通过
        except Exception as e:
            self.error_logger.error(f"tar命令验证异常: {file_path}, 错误: {str(e)}")
            return False
    
    def get_package_info(self, file_path):
        """获取包的基本信息"""
        try:
            if not os.path.exists(file_path):
                return None
            
            file_stat = os.stat(file_path)
            file_ext = self._get_file_extension(file_path)
            
            info = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_size': file_stat.st_size,
                'file_extension': file_ext,
                'is_supported': self._is_supported_format(file_ext),
                'modified_time': file_stat.st_mtime
            }
            
            # 如果是支持的压缩格式，尝试获取内容信息
            if info['is_supported']:
                try:
                    if file_ext in ['.tar.gz', '.tar.bz2', '.tar.xz', '.tar']:
                        with tarfile.open(file_path, 'r:*') as tar:
                            members = tar.getmembers()
                            info['content_count'] = len(members)
                            info['content_size'] = sum(m.size for m in members if m.isfile())
                    elif file_ext == '.zip':
                        with zipfile.ZipFile(file_path, 'r') as zip_file:
                            file_list = zip_file.namelist()
                            info['content_count'] = len(file_list)
                            info['content_size'] = sum(zip_file.getinfo(f).file_size for f in file_list if not f.endswith('/'))
                except:
                    # 如果获取内容信息失败，不影响基本信息
                    pass
            
            return info
            
        except Exception as e:
            self.error_logger.error(f"获取包信息失败: {file_path}, 错误: {str(e)}")
            return None
