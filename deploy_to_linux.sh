#!/bin/bash
# 摆渡机系统Linux部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
APP_NAME="ferry-system"
APP_USER="ferry"
APP_DIR="/opt/ferry-system"
SERVICE_FILE="/etc/systemd/system/ferry-system.service"
DEPLOY_SOURCE="./deploy"

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检查部署文件
check_deploy_files() {
    log_info "检查部署文件..."
    
    if [[ ! -d "$DEPLOY_SOURCE" ]]; then
        log_error "部署目录不存在: $DEPLOY_SOURCE"
        log_info "请先运行: python build_encrypted.py"
        exit 1
    fi
    
    required_files=("ferry_system" "start.sh" "ferry-system.service" "DEPLOY_README.md")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$DEPLOY_SOURCE/$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_success "部署文件检查完成"
}

# 创建应用用户
create_app_user() {
    log_info "创建应用用户: $APP_USER"
    
    if id "$APP_USER" &>/dev/null; then
        log_warning "用户 $APP_USER 已存在"
    else
        useradd -r -s /bin/false -d "$APP_DIR" "$APP_USER"
        log_success "用户 $APP_USER 创建成功"
    fi
}

# 创建应用目录
create_app_directory() {
    log_info "创建应用目录: $APP_DIR"
    
    mkdir -p "$APP_DIR"
    mkdir -p "$APP_DIR/data"
    mkdir -p "$APP_DIR/logs"
    
    log_success "应用目录创建完成"
}

# 复制应用文件
copy_app_files() {
    log_info "复制应用文件..."
    
    # 复制可执行文件
    cp "$DEPLOY_SOURCE/ferry_system" "$APP_DIR/"
    chmod +x "$APP_DIR/ferry_system"
    
    # 复制启动脚本
    cp "$DEPLOY_SOURCE/start.sh" "$APP_DIR/"
    chmod +x "$APP_DIR/start.sh"
    
    # 复制文档
    cp "$DEPLOY_SOURCE/DEPLOY_README.md" "$APP_DIR/"
    
    log_success "应用文件复制完成"
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."
    
    chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    chmod 755 "$APP_DIR"
    chmod 755 "$APP_DIR/ferry_system"
    chmod 755 "$APP_DIR/start.sh"
    chmod 644 "$APP_DIR/DEPLOY_README.md"
    
    log_success "文件权限设置完成"
}

# 安装systemd服务
install_systemd_service() {
    log_info "安装systemd服务..."
    
    # 读取加密密钥
    if [[ -f "$DEPLOY_SOURCE/DEPLOY_README.md" ]]; then
        ENCRYPTION_KEY=$(grep -A 3 "加密密钥" "$DEPLOY_SOURCE/DEPLOY_README.md" | tail -1 | tr -d '`')
        if [[ -z "$ENCRYPTION_KEY" ]]; then
            log_warning "无法从README中提取加密密钥"
            read -p "请输入加密密钥: " ENCRYPTION_KEY
        fi
    else
        read -p "请输入加密密钥: " ENCRYPTION_KEY
    fi
    
    # 创建服务文件
    cat > "$SERVICE_FILE" << EOF
[Unit]
Description=Ferry System - Artifact Sync Service
After=network.target

[Service]
Type=simple
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
Environment=FERRY_ENCRYPTION_KEY=$ENCRYPTION_KEY
ExecStart=$APP_DIR/ferry_system
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_success "systemd服务安装完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    # 启用服务
    systemctl enable "$APP_NAME"
    
    # 启动服务
    systemctl start "$APP_NAME"
    
    # 检查状态
    sleep 3
    if systemctl is-active --quiet "$APP_NAME"; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        log_info "查看日志: journalctl -u $APP_NAME -f"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    systemctl status "$APP_NAME" --no-pager
    
    echo ""
    log_info "有用的命令:"
    echo "  查看状态: sudo systemctl status $APP_NAME"
    echo "  查看日志: sudo journalctl -u $APP_NAME -f"
    echo "  重启服务: sudo systemctl restart $APP_NAME"
    echo "  停止服务: sudo systemctl stop $APP_NAME"
    echo "  禁用服务: sudo systemctl disable $APP_NAME"
    
    echo ""
    log_info "应用目录: $APP_DIR"
    log_info "配置文件: $SERVICE_FILE"
    log_info "数据目录: $APP_DIR/data"
    log_info "日志目录: $APP_DIR/logs"
}

# 安装依赖
install_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查操作系统
    if [[ -f /etc/redhat-release ]]; then
        # CentOS/RHEL
        log_info "检测到 CentOS/RHEL 系统"
        yum update -y
        yum install -y curl wget unzip
    elif [[ -f /etc/debian_version ]]; then
        # Ubuntu/Debian
        log_info "检测到 Ubuntu/Debian 系统"
        apt-get update
        apt-get install -y curl wget unzip
    else
        log_warning "未识别的操作系统，请手动安装依赖: curl, wget, unzip"
    fi
    
    log_success "系统依赖检查完成"
}

# 创建防火墙规则
setup_firewall() {
    log_info "配置防火墙..."
    
    # 检查防火墙类型
    if command -v firewall-cmd &> /dev/null; then
        # firewalld (CentOS/RHEL)
        firewall-cmd --permanent --add-port=5000/tcp
        firewall-cmd --reload
        log_success "firewalld 规则添加完成"
    elif command -v ufw &> /dev/null; then
        # ufw (Ubuntu)
        ufw allow 5000/tcp
        log_success "ufw 规则添加完成"
    else
        log_warning "未检测到防火墙，请手动开放端口 5000"
    fi
}

# 主函数
main() {
    echo "🚀 摆渡机系统 Linux 部署脚本"
    echo "=================================="
    
    # 检查权限
    check_root
    
    # 检查部署文件
    check_deploy_files
    
    # 确认部署
    echo ""
    read -p "是否继续部署? (y/n): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    log_info "开始部署..."
    
    install_dependencies
    create_app_user
    create_app_directory
    copy_app_files
    set_permissions
    install_systemd_service
    setup_firewall
    start_service
    
    echo ""
    echo "🎉 部署完成!"
    echo "=================================="
    show_status
    
    echo ""
    log_success "摆渡机系统已成功部署到 Linux 服务器"
    log_info "访问地址: http://$(hostname -I | awk '{print $1}'):5000"
}

# 运行主函数
main "$@"
