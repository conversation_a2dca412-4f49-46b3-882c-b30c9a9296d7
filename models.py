from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class Artifact(db.Model):
    """制品模型"""
    __tablename__ = 'artifacts'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)  # 制品包名称
    artifact_id = db.Column(db.String(100), unique=True, nullable=False)  # 制品包ID
    version = db.Column(db.String(100), nullable=False)  # 版本名称
    version_id = db.Column(db.String(100), nullable=False)  # 版本ID
    status = db.Column(db.String(50), nullable=False)  # 状态：预发布、生产
    description = db.Column(db.Text)  # 版本备注
    file_size = db.Column(db.BigInteger)  # 文件大小
    checksum = db.Column(db.String(64))  # 文件校验和
    download_url = db.Column(db.String(500))  # 下载地址
    environment = db.Column(db.String(50))  # 环境标识
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'artifact_id': self.artifact_id,
            'version': self.version,
            'version_id': self.version_id,
            'status': self.status,
            'description': self.description,
            'file_size': self.file_size,
            'checksum': self.checksum,
            'download_url': self.download_url,
            'environment': self.environment,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class SyncRecord(db.Model):
    """同步记录模型"""
    __tablename__ = 'sync_records'
    
    id = db.Column(db.Integer, primary_key=True)
    artifact_id = db.Column(db.String(100), nullable=False)
    artifact_name = db.Column(db.String(255), nullable=False)
    version = db.Column(db.String(100), nullable=False)
    source_env = db.Column(db.String(50), nullable=False)  # 源环境
    target_env = db.Column(db.String(50), nullable=False)  # 目标环境
    status = db.Column(db.String(50), nullable=False)  # 同步状态：success, failed, in_progress
    error_message = db.Column(db.Text)  # 错误信息
    retry_count = db.Column(db.Integer, default=0)  # 重试次数
    sync_type = db.Column(db.String(50), default='auto')  # 同步类型：auto, manual
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    def to_dict(self):
        return {
            'id': self.id,
            'artifact_id': self.artifact_id,
            'artifact_name': self.artifact_name,
            'version': self.version,
            'source_env': self.source_env,
            'target_env': self.target_env,
            'status': self.status,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'sync_type': self.sync_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }

class Environment(db.Model):
    """环境配置模型"""
    __tablename__ = 'environments'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False)  # 环境代码
    name = db.Column(db.String(100), nullable=False)  # 环境名称
    url = db.Column(db.String(500), nullable=False)  # 环境URL
    token = db.Column(db.String(255))  # 访问令牌
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    config = db.Column(db.Text)  # 额外配置（JSON格式）
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'url': self.url,
            'is_active': self.is_active,
            'config': json.loads(self.config) if self.config else {},
            'created_at': self.created_at.isoformat() if self.created_at else None
        }