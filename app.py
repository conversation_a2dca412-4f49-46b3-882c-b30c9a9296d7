from flask import Flask, request, jsonify
from flask.views import MethodView
from datetime import datetime, timedelta
import threading
import time
import hashlib
import requests
import os
import logging
from logging.handlers import RotatingFileHandler
from flasgger import Swagger, swag_from
from config import Config
from models import db, Artifact, SyncRecord, Environment
from services.artifact_service import ArtifactService
from services.sync_service import SyncService
from services.notification_service import NotificationService
from services.cmdb_service import CMDBService
from services.package_validator import PackageValidator

app = Flask(__name__)
app.config.from_object(Config)

# 初始化Swagger
swagger_config = {
    "headers": [],
    "specs": [
        {
            "endpoint": 'apispec',
            "route": '/apispec.json',
            "rule_filter": lambda rule: True,
            "model_filter": lambda tag: True,
        }
    ],
    "static_url_path": "/flasgger_static",
    "swagger_ui": True,
    "specs_route": "/api/docs/"
}

swagger_template = {
    "swagger": "2.0",
    "info": {
        "title": "摆渡机制品同步系统 API",
        "description": "基于Flask的制品同步系统，用于在不同环境间自动同步制品包",
        "version": "1.0.0",
        "contact": {
            "name": "API Support",
            "email": "<EMAIL>"
        }
    },
    "host": "localhost:5000",
    "basePath": "/",
    "schemes": ["http", "https"],
    "consumes": ["application/json"],
    "produces": ["application/json"],
    "tags": [
        {
            "name": "制品管理",
            "description": "制品包的创建、查询和管理"
        },
        {
            "name": "同步管理",
            "description": "制品同步相关操作"
        },
        {
            "name": "统一接口",
            "description": "符合统一规范的接口"
        },
        {
            "name": "系统监控",
            "description": "系统健康检查和监控"
        }
    ]
}

swagger = Swagger(app, config=swagger_config, template=swagger_template)

# 初始化数据库
db.init_app(app)

# 初始化服务
artifact_service = ArtifactService()
sync_service = SyncService()
notification_service = NotificationService()
cmdb_service = CMDBService()
package_validator = PackageValidator()

# 配置日志
def setup_logging():
    """配置日志系统"""
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 配置根日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler(),  # 控制台输出
            RotatingFileHandler(
                'logs/ferry_system.log',
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
        ]
    )
    
    # 配置同步专用日志
    sync_logger = logging.getLogger('sync')
    sync_handler = RotatingFileHandler(
        'logs/sync.log',
        maxBytes=50*1024*1024,  # 50MB
        backupCount=10,
        encoding='utf-8'
    )
    sync_handler.setFormatter(logging.Formatter(
        '%(asctime)s [%(levelname)s] %(message)s'
    ))
    sync_logger.addHandler(sync_handler)
    sync_logger.setLevel(logging.INFO)
    
    # 配置错误专用日志
    error_logger = logging.getLogger('error')
    error_handler = RotatingFileHandler(
        'logs/error.log',
        maxBytes=20*1024*1024,  # 20MB
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setFormatter(logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    ))
    error_logger.addHandler(error_handler)
    error_logger.setLevel(logging.ERROR)

setup_logging()
logger = logging.getLogger(__name__)
sync_logger = logging.getLogger('sync')
error_logger = logging.getLogger('error')

class ArtifactAPI(MethodView):
    """制品管理API"""
    
    def get(self, artifact_id=None):
        """获取制品信息
        ---
        tags:
          - 制品管理
        parameters:
          - name: artifact_id
            in: path
            type: integer
            required: false
            description: 制品ID
          - name: status
            in: query
            type: string
            required: false
            description: 制品状态过滤（预发布、生产）
        responses:
          200:
            description: 成功获取制品信息
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 制品ID
                name:
                  type: string
                  description: 制品包名称
                artifact_id:
                  type: string
                  description: 制品包ID
                version:
                  type: string
                  description: 版本名称
                version_id:
                  type: string
                  description: 版本ID
                status:
                  type: string
                  description: 状态
                description:
                  type: string
                  description: 版本备注
                file_size:
                  type: integer
                  description: 文件大小
                checksum:
                  type: string
                  description: 文件校验和
                download_url:
                  type: string
                  description: 下载地址
                environment:
                  type: string
                  description: 环境标识
                created_at:
                  type: string
                  description: 创建时间
                updated_at:
                  type: string
                  description: 更新时间
          404:
            description: 制品不存在
            schema:
              type: object
              properties:
                error:
                  type: string
                  description: 错误信息
        """
        if artifact_id:
            artifact = artifact_service.get_artifact(artifact_id)
            if not artifact:
                logger.warning(f"制品不存在: {artifact_id}")
                return jsonify({"error": "制品不存在"}), 404
            logger.info(f"获取制品详情: {artifact_id}")
            return jsonify(artifact.to_dict())
        
        # 获取制品列表
        status = request.args.get('status')
        artifacts = artifact_service.get_artifacts(status=status)
        logger.info(f"获取制品列表, 状态过滤: {status}, 数量: {len(artifacts)}")
        return jsonify([a.to_dict() for a in artifacts])
    
    def post(self):
        """创建制品记录
        ---
        tags:
          - 制品管理
        parameters:
          - name: body
            in: body
            required: true
            description: 制品信息
            schema:
              type: object
              required:
                - name
                - artifact_id
                - version
                - version_id
                - status
              properties:
                name:
                  type: string
                  description: 制品包名称
                  example: "percat_UAT"
                artifact_id:
                  type: string
                  description: 制品包ID（唯一）
                  example: "percat_UAT_001"
                version:
                  type: string
                  description: 版本名称
                  example: "1.2.3"
                version_id:
                  type: string
                  description: 版本ID
                  example: "v123"
                status:
                  type: string
                  description: 状态（预发布、生产）
                  example: "预发布"
                description:
                  type: string
                  description: 版本备注
                  example: "新功能发布"
                file_size:
                  type: integer
                  description: 文件大小（字节）
                  example: 1024000
                checksum:
                  type: string
                  description: 文件校验和
                  example: "abc123def456"
                download_url:
                  type: string
                  description: 下载地址
                  example: "http://example.com/download/percat_UAT_001"
                environment:
                  type: string
                  description: 环境标识
                  example: "UAT"
        responses:
          201:
            description: 制品创建成功
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 制品ID
                name:
                  type: string
                  description: 制品包名称
                artifact_id:
                  type: string
                  description: 制品包ID
                version:
                  type: string
                  description: 版本名称
                status:
                  type: string
                  description: 状态
                created_at:
                  type: string
                  description: 创建时间
          400:
            description: 请求参数错误
            schema:
              type: object
              properties:
                error:
                  type: string
                  description: 错误信息
        """
        data = request.json
        try:
            artifact = artifact_service.create_artifact(data)
            logger.info(f"创建制品记录成功: {artifact.artifact_id} v{artifact.version}")
            return jsonify(artifact.to_dict()), 201
        except Exception as e:
            error_logger.error(f"创建制品记录失败: {str(e)}, 数据: {data}")
            return jsonify({"error": str(e)}), 400

class SyncAPI(MethodView):
    """同步管理API"""
    
    def post(self):
        """手动触发同步
        ---
        tags:
          - 同步管理
        parameters:
          - name: body
            in: body
            required: true
            description: 同步参数
            schema:
              type: object
              required:
                - artifact_id
                - target_env
              properties:
                artifact_id:
                  type: string
                  description: 制品ID
                  example: "percat_UAT_001"
                target_env:
                  type: string
                  description: 目标环境
                  enum: ["PROD_INTERNAL", "TEST_EXTERNAL", "TEST_INTERNAL"]
                  example: "TEST_EXTERNAL"
        responses:
          200:
            description: 同步成功
            schema:
              type: object
              properties:
                status:
                  type: string
                  description: 同步状态
                  example: "success"
                artifact_id:
                  type: string
                  description: 制品ID
                target_env:
                  type: string
                  description: 目标环境
                sync_record_id:
                  type: integer
                  description: 同步记录ID
                duration:
                  type: number
                  description: 同步耗时（秒）
          400:
            description: 请求参数错误
            schema:
              type: object
              properties:
                error:
                  type: string
                  description: 错误信息
        """
        data = request.json
        artifact_id = data.get('artifact_id')
        target_env = data.get('target_env')
        
        logger.info(f"手动触发同步: {artifact_id} -> {target_env}")
        
        try:
            result = sync_service.manual_sync(artifact_id, target_env)
            logger.info(f"手动同步完成: {artifact_id} -> {target_env}, 结果: {result['status']}")
            return jsonify(result)
        except Exception as e:
            error_logger.error(f"手动同步失败: {artifact_id} -> {target_env}, 错误: {str(e)}")
            return jsonify({"error": str(e)}), 400
    
    def get(self):
        """获取同步记录
        ---
        tags:
          - 同步管理
        responses:
          200:
            description: 成功获取同步记录
            schema:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: integer
                    description: 同步记录ID
                  artifact_id:
                    type: string
                    description: 制品ID
                  artifact_name:
                    type: string
                    description: 制品名称
                  version:
                    type: string
                    description: 版本名称
                  source_env:
                    type: string
                    description: 源环境
                  target_env:
                    type: string
                    description: 目标环境
                  status:
                    type: string
                    description: 同步状态
                    enum: ["success", "failed", "in_progress"]
                  error_message:
                    type: string
                    description: 错误信息
                  retry_count:
                    type: integer
                    description: 重试次数
                  sync_type:
                    type: string
                    description: 同步类型
                    enum: ["auto", "manual"]
                  created_at:
                    type: string
                    description: 创建时间
                  completed_at:
                    type: string
                    description: 完成时间
        """
        records = SyncRecord.query.order_by(SyncRecord.created_at.desc()).limit(50).all()
        logger.info(f"获取同步记录: {len(records)} 条")
        return jsonify([r.to_dict() for r in records])

@app.route('/uniform/api/v1/sync/artifact', methods=['GET'])
def sync_by_date():
    """指定时间同步制品
    ---
    tags:
      - 统一接口
    parameters:
      - name: date
        in: query
        type: string
        required: true
        description: 日期（YYYYMMDD格式，如：20150721）
        example: "20240115"
    responses:
      200:
        description: 同步成功
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码（0=成功）
              example: 0
            error:
              type: string
              description: 错误信息
              example: ""
            message:
              type: string
              description: 返回消息
              example: "同步完成"
            data:
              type: array
              items:
                type: object
                properties:
                  artifactName:
                    type: string
                    description: 制品名称
                  artifactId:
                    type: string
                    description: 制品ID
                  versionName:
                    type: string
                    description: 版本名称
                  versionId:
                    type: string
                    description: 版本ID
                  memo:
                    type: string
                    description: 版本备注
                  date:
                    type: string
                    description: 日期
                  status:
                    type: string
                    description: 同步状态
                  targetEnv:
                    type: string
                    description: 目标环境
      400:
        description: 参数错误
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 错误码
              example: 100000
            error:
              type: string
              description: 错误信息
            message:
              type: string
              description: 错误描述
            data:
              type: object
              nullable: true
    """
    date_str = request.args.get('date')

    if not date_str:
        return jsonify({
            "code": 100000,
            "error": "参数结构错误",
            "message": "缺少date参数",
            "data": None
        }), 400

    logger.info(f"指定时间同步制品: {date_str}")

    try:
        # 将日期格式从 YYYYMMDD 转换为 YYYY-MM-DD
        if len(date_str) == 8:
            formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            sync_date = datetime.strptime(formatted_date, '%Y-%m-%d')
        else:
            sync_date = datetime.strptime(date_str, '%Y-%m-%d')

        result = sync_service.sync_by_date_unified(sync_date)
        logger.info(f"指定时间同步完成: {date_str}, 处理 {len(result)} 个制品")

        return jsonify({
            "code": 0,
            "error": "",
            "message": "同步完成",
            "data": result
        })
    except ValueError as e:
        error_logger.error(f"日期格式错误: {date_str}, 错误: {str(e)}")
        return jsonify({
            "code": 133503,
            "error": "参数数据类型校验不通过",
            "message": f"日期格式错误: {str(e)}",
            "data": None
        }), 400
    except Exception as e:
        error_logger.error(f"指定时间同步失败: {date_str}, 错误: {str(e)}")
        return jsonify({
            "code": 100000,
            "error": str(e),
            "message": "同步失败",
            "data": None
        }), 500

@app.route('/uniform/api/v1/sync/artifact/by-name', methods=['GET'])
def sync_by_artifact_name():
    """指定程序包名称同步制品
    ---
    tags:
      - 统一接口
    parameters:
      - name: artifactName
        in: query
        type: string
        required: true
        description: 程序包名称
        example: "percat_UAT"
    responses:
      200:
        description: 同步成功
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码（0=成功）
              example: 0
            error:
              type: string
              description: 错误信息
              example: ""
            message:
              type: string
              description: 返回消息
              example: "同步完成"
            data:
              type: array
              items:
                type: object
                properties:
                  artifactName:
                    type: string
                    description: 制品名称
                  artifactId:
                    type: string
                    description: 制品ID
                  versionName:
                    type: string
                    description: 版本名称
                  versionId:
                    type: string
                    description: 版本ID
                  memo:
                    type: string
                    description: 版本备注
                  date:
                    type: string
                    description: 日期
                  status:
                    type: string
                    description: 同步状态
                  targetEnv:
                    type: string
                    description: 目标环境
      400:
        description: 参数错误
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 错误码
              example: 100000
            error:
              type: string
              description: 错误信息
            message:
              type: string
              description: 错误描述
            data:
              type: object
              nullable: true
    """
    artifact_name = request.args.get('artifactName')

    if not artifact_name:
        return jsonify({
            "code": 100000,
            "error": "参数结构错误",
            "message": "缺少artifactName参数",
            "data": None
        }), 400

    logger.info(f"指定程序包名称同步制品: {artifact_name}")

    try:
        result = sync_service.sync_by_artifact_name(artifact_name)
        logger.info(f"程序包同步完成: {artifact_name}, 处理 {len(result)} 个制品")

        return jsonify({
            "code": 0,
            "error": "",
            "message": "同步完成",
            "data": result
        })
    except Exception as e:
        error_logger.error(f"程序包同步失败: {artifact_name}, 错误: {str(e)}")
        return jsonify({
            "code": 100000,
            "error": str(e),
            "message": "同步失败",
            "data": None
        }), 500

@app.route('/uniform/api/v1/sync/records', methods=['GET'])
def get_sync_records_by_date():
    """指定时间查询同步记录
    ---
    tags:
      - 统一接口
    parameters:
      - name: date
        in: query
        type: string
        required: true
        description: 日期（YYYYMMDD格式，如：20150721）
        example: "20240115"
    responses:
      200:
        description: 查询成功
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码（0=成功）
              example: 0
            error:
              type: string
              description: 错误信息
              example: ""
            message:
              type: string
              description: 返回消息
              example: "查询完成"
            data:
              type: array
              items:
                type: object
                properties:
                  syncId:
                    type: integer
                    description: 同步记录ID
                  artifactName:
                    type: string
                    description: 制品名称
                  artifactId:
                    type: string
                    description: 制品ID
                  versionName:
                    type: string
                    description: 版本名称
                  sourceEnv:
                    type: string
                    description: 源环境
                  targetEnv:
                    type: string
                    description: 目标环境
                  status:
                    type: string
                    description: 同步状态
                    enum: ["success", "failed", "in_progress"]
                  syncType:
                    type: string
                    description: 同步类型
                    enum: ["auto", "manual"]
                  errorMessage:
                    type: string
                    description: 错误信息
                  retryCount:
                    type: integer
                    description: 重试次数
                  createdAt:
                    type: string
                    description: 创建时间
                  completedAt:
                    type: string
                    description: 完成时间
      400:
        description: 参数错误
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 错误码
              example: 100000
            error:
              type: string
              description: 错误信息
            message:
              type: string
              description: 错误描述
            data:
              type: object
              nullable: true
    """
    date_str = request.args.get('date')

    if not date_str:
        return jsonify({
            "code": 100000,
            "error": "参数结构错误",
            "message": "缺少date参数",
            "data": None
        }), 400

    logger.info(f"指定时间查询同步记录: {date_str}")

    try:
        # 将日期格式从 YYYYMMDD 转换为 YYYY-MM-DD
        if len(date_str) == 8:
            formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            query_date = datetime.strptime(formatted_date, '%Y-%m-%d')
        else:
            query_date = datetime.strptime(date_str, '%Y-%m-%d')

        result = sync_service.get_sync_records_by_date(query_date)
        logger.info(f"查询同步记录完成: {date_str}, 找到 {len(result)} 条记录")

        return jsonify({
            "code": 0,
            "error": "",
            "message": "查询完成",
            "data": result
        })
    except ValueError as e:
        error_logger.error(f"日期格式错误: {date_str}, 错误: {str(e)}")
        return jsonify({
            "code": 133503,
            "error": "参数数据类型校验不通过",
            "message": f"日期格式错误: {str(e)}",
            "data": None
        }), 400
    except Exception as e:
        error_logger.error(f"查询同步记录失败: {date_str}, 错误: {str(e)}")
        return jsonify({
            "code": 100000,
            "error": str(e),
            "message": "查询失败",
            "data": None
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查
    ---
    tags:
      - 系统监控
    responses:
      200:
        description: 系统健康状态
        schema:
          type: object
          properties:
            status:
              type: string
              description: 健康状态
              example: "healthy"
            timestamp:
              type: string
              description: 当前时间戳
              example: "2024-01-15T10:30:00.123456"
            version:
              type: string
              description: 系统版本
              example: "1.0.0"
    """
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    })

def scheduled_sync_task():
    """定时同步任务 - 每5分钟执行一次"""
    while True:
        try:
            logger.info("开始执行定时同步任务")
            sync_service.check_and_sync_new_versions()
            logger.info("定时同步任务完成")
        except Exception as e:
            error_logger.error(f"定时同步任务失败: {str(e)}")
            notification_service.send_error_notification(str(e))
        
        time.sleep(300)  # 5分钟

# 注册API路由
app.add_url_rule('/api/artifacts', view_func=ArtifactAPI.as_view('artifacts'))
app.add_url_rule('/api/artifacts/<int:artifact_id>', view_func=ArtifactAPI.as_view('artifact'))
app.add_url_rule('/api/sync', view_func=SyncAPI.as_view('sync'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    
    logger.info("摆渡机系统启动")
    
    # 启动定时任务
    sync_thread = threading.Thread(target=scheduled_sync_task, daemon=True)
    sync_thread.start()
    logger.info("定时同步任务已启动")
    
    app.run(debug=True, host='0.0.0.0', port=5000)

