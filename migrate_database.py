#!/usr/bin/env python3
"""
数据库迁移脚本 - 将数据库从instance目录迁移到data目录
"""

import os
import shutil
import sqlite3
from pathlib import Path

def check_database_exists(db_path):
    """检查数据库是否存在且有效"""
    if not os.path.exists(db_path):
        return False, "数据库文件不存在"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        conn.close()
        
        if tables:
            return True, f"数据库有效，包含 {len(tables)} 个表"
        else:
            return True, "数据库为空"
            
    except Exception as e:
        return False, f"数据库损坏: {str(e)}"

def migrate_database():
    """迁移数据库"""
    print("🔄 数据库迁移工具")
    print("=" * 50)
    
    # 源路径和目标路径
    source_path = "instance/ferry_system.db"
    target_dir = "data"
    target_path = "data/ferry_system.db"
    
    print(f"源路径: {source_path}")
    print(f"目标路径: {target_path}")
    
    # 检查源数据库
    if os.path.exists(source_path):
        is_valid, message = check_database_exists(source_path)
        print(f"源数据库状态: {message}")
        
        if not is_valid:
            print("❌ 源数据库无效，无法迁移")
            return False
    else:
        print("ℹ️  源数据库不存在，将创建新的数据库")
        return True
    
    # 创建目标目录
    os.makedirs(target_dir, exist_ok=True)
    print(f"✅ 创建目标目录: {target_dir}")
    
    # 检查目标是否已存在
    if os.path.exists(target_path):
        choice = input(f"⚠️  目标文件已存在: {target_path}\n是否覆盖? (y/n): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("❌ 用户取消操作")
            return False
    
    try:
        # 复制数据库文件
        shutil.copy2(source_path, target_path)
        print(f"✅ 数据库复制成功: {source_path} -> {target_path}")
        
        # 验证目标数据库
        is_valid, message = check_database_exists(target_path)
        if is_valid:
            print(f"✅ 目标数据库验证成功: {message}")
        else:
            print(f"❌ 目标数据库验证失败: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {str(e)}")
        return False

def cleanup_instance_directory():
    """清理instance目录"""
    print("\n🧹 清理instance目录")
    print("=" * 30)
    
    instance_dir = "instance"
    
    if not os.path.exists(instance_dir):
        print("ℹ️  instance目录不存在")
        return True
    
    # 列出目录内容
    try:
        files = os.listdir(instance_dir)
        if not files:
            print("ℹ️  instance目录为空")
        else:
            print("instance目录内容:")
            for file in files:
                file_path = os.path.join(instance_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  📄 {file} ({size} bytes)")
                else:
                    print(f"  📁 {file}/")
        
        # 询问是否删除
        if files:
            choice = input("\n是否删除instance目录及其内容? (y/n): ").strip().lower()
            if choice in ['y', 'yes']:
                shutil.rmtree(instance_dir)
                print("✅ instance目录已删除")
                return True
            else:
                print("ℹ️  保留instance目录")
                return True
        else:
            # 空目录直接删除
            os.rmdir(instance_dir)
            print("✅ 空的instance目录已删除")
            return True
            
    except Exception as e:
        print(f"❌ 清理instance目录失败: {str(e)}")
        return False

def update_gitignore():
    """更新.gitignore文件"""
    print("\n📝 更新.gitignore")
    print("=" * 20)
    
    gitignore_path = ".gitignore"
    
    # 要添加的忽略规则
    ignore_rules = [
        "# 数据目录",
        "data/",
        "*.db",
        "*.db-journal"
    ]
    
    try:
        # 读取现有内容
        existing_content = ""
        if os.path.exists(gitignore_path):
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        
        # 检查是否需要添加规则
        rules_to_add = []
        for rule in ignore_rules:
            if rule not in existing_content:
                rules_to_add.append(rule)
        
        if rules_to_add:
            # 添加新规则
            with open(gitignore_path, 'a', encoding='utf-8') as f:
                if existing_content and not existing_content.endswith('\n'):
                    f.write('\n')
                f.write('\n')
                for rule in rules_to_add:
                    f.write(rule + '\n')
            
            print(f"✅ 已添加 {len(rules_to_add)} 条忽略规则到 .gitignore")
            for rule in rules_to_add:
                if not rule.startswith('#'):
                    print(f"  + {rule}")
        else:
            print("ℹ️  .gitignore 已包含相关规则")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新.gitignore失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 摆渡机系统 - 数据库迁移工具")
    print("=" * 60)
    print("此工具将帮助你:")
    print("1. 将数据库从 instance/ 迁移到 data/ 目录")
    print("2. 清理 instance 目录")
    print("3. 更新 .gitignore 文件")
    print("=" * 60)
    
    # 确认操作
    choice = input("是否继续? (y/n): ").strip().lower()
    if choice not in ['y', 'yes']:
        print("❌ 用户取消操作")
        return
    
    success = True
    
    # 1. 迁移数据库
    if not migrate_database():
        success = False
    
    # 2. 清理instance目录
    if success:
        if not cleanup_instance_directory():
            success = False
    
    # 3. 更新.gitignore
    if success:
        if not update_gitignore():
            success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 迁移完成!")
        print("\n📋 后续步骤:")
        print("1. 重启应用: python app.py")
        print("2. 验证数据库连接正常")
        print("3. 检查数据是否完整")
        print("4. 提交代码变更")
        
        print("\n📁 新的目录结构:")
        print("  data/ferry_system.db  <- 数据库文件")
        print("  logs/                 <- 日志文件")
        print("  (instance/ 目录已清理)")
    else:
        print("❌ 迁移过程中出现错误")
        print("请检查错误信息并手动处理")

if __name__ == "__main__":
    main()
