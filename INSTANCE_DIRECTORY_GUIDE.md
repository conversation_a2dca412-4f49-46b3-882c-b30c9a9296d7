# Instance 目录说明

## 什么是 instance 目录？

`instance` 目录是 Flask 应用的**实例文件夹**，用于存储特定于应用实例的文件。

## 当前内容

```
instance/
└── ferry_system.db  # SQLite 数据库文件
```

## 作用说明

### 1. **数据库存储**
- 存储 SQLite 数据库文件
- 包含所有制品记录和同步历史
- **重要**: 删除会丢失所有数据

### 2. **Flask 默认行为**
- Flask 自动创建 instance 目录
- 用于存储实例特定的配置和数据
- 相对路径 `sqlite:///ferry_system.db` 会自动存储到 instance 目录

## 是否可以去掉？

### ❌ **直接删除的风险**
- 丢失所有制品记录
- 丢失同步历史数据
- 应用启动时需要重新初始化数据库

### ✅ **安全的处理方式**

#### 方案1: 迁移数据库到其他目录
```bash
# 使用迁移脚本
python migrate_database.py
```

#### 方案2: 保留但添加到 .gitignore
```bash
# 在 .gitignore 中添加
instance/
*.db
```

#### 方案3: 使用其他数据库
```python
# 修改 config.py 使用 PostgreSQL 或 MySQL
SQLALCHEMY_DATABASE_URI = 'postgresql://user:pass@localhost/ferry_system'
```

## 推荐操作

### 🎯 **推荐方案**: 迁移到 data 目录

1. **运行迁移脚本**
   ```bash
   python migrate_database.py
   ```

2. **迁移后的目录结构**
   ```
   项目根目录/
   ├── data/
   │   └── ferry_system.db  # 迁移后的数据库
   ├── logs/                # 日志文件
   ├── services/            # 服务代码
   ├── app.py              # 主应用
   └── config.py           # 配置文件
   ```

3. **更新配置**
   ```python
   # config.py
   SQLALCHEMY_DATABASE_URI = 'sqlite:///data/ferry_system.db'
   ```

4. **更新 .gitignore**
   ```
   # 数据目录
   data/
   *.db
   *.db-journal
   ```

## 迁移步骤

### 1. 备份数据
```bash
# 备份当前数据库
cp instance/ferry_system.db ferry_system_backup.db
```

### 2. 运行迁移脚本
```bash
python migrate_database.py
```

### 3. 验证迁移
```bash
# 启动应用
python app.py

# 检查数据是否完整
curl http://localhost:5000/api/artifacts
curl http://localhost:5000/api/sync
```

### 4. 清理（可选）
```bash
# 确认数据正常后删除备份
rm ferry_system_backup.db
```

## 注意事项

### ⚠️ **重要提醒**
- 迁移前务必备份数据库
- 确认新位置的数据库工作正常后再删除原文件
- 更新配置文件中的数据库路径

### 📁 **目录权限**
- 确保新目录有读写权限
- 数据库文件需要应用进程的访问权限

### 🔄 **回滚方案**
如果迁移出现问题：
```bash
# 恢复原配置
git checkout config.py

# 恢复数据库
cp ferry_system_backup.db instance/ferry_system.db

# 重启应用
python app.py
```

## 总结

### ✅ **可以去掉 instance 目录**
- 通过迁移数据库到其他位置
- 使用迁移脚本确保数据安全
- 更新配置和 .gitignore

### 🎯 **推荐做法**
1. 使用 `migrate_database.py` 脚本
2. 将数据库迁移到 `data/` 目录
3. 更新配置文件
4. 添加适当的 .gitignore 规则

这样既保持了数据的完整性，又让项目结构更加清晰！
