import requests
import json
import logging
from datetime import datetime
from config import Config

class CMDBService:
    """CMDB服务"""
    
    def __init__(self):
        self.config = Config()
        self.cmdb_config = self.config.CMDB_CONFIG
        self.base_url = self.cmdb_config['base_url']
        self.project_id = self.cmdb_config['project_id']
        self.api_token = self.cmdb_config['api_token']
        self.endpoints = self.cmdb_config['endpoints']
        self.logger = logging.getLogger(__name__)
        self.sync_logger = logging.getLogger('sync')
        self.error_logger = logging.getLogger('error')
    
    def _get_headers(self):
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json',
            'User-Agent': 'Ferry-System/1.0.0'
        }
    
    def _make_request(self, method, endpoint, data=None, params=None):
        """发起HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()
        
        try:
            self.sync_logger.info(f"CMDB请求: {method} {url}")
            
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data, timeout=30)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data, timeout=30)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            
            if response.content:
                return response.json()
            else:
                return {}
                
        except requests.exceptions.RequestException as e:
            error_msg = f"CMDB请求失败: {method} {url}, 错误: {str(e)}"
            self.error_logger.error(error_msg)
            raise Exception(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"CMDB响应解析失败: {str(e)}"
            self.error_logger.error(error_msg)
            raise Exception(error_msg)
    
    def create_sync_record(self, sync_record_data):
        """在CMDB中创建同步记录"""
        try:
            self.sync_logger.info(f"创建CMDB同步记录: {sync_record_data.get('artifact_id')}")
            
            # 构建CMDB记录数据
            cmdb_data = {
                'recordType': 'sync_record',
                'artifactId': sync_record_data.get('artifact_id'),
                'artifactName': sync_record_data.get('artifact_name'),
                'version': sync_record_data.get('version'),
                'sourceEnv': sync_record_data.get('source_env'),
                'targetEnv': sync_record_data.get('target_env'),
                'status': sync_record_data.get('status'),
                'syncType': sync_record_data.get('sync_type'),
                'errorMessage': sync_record_data.get('error_message'),
                'retryCount': sync_record_data.get('retry_count', 0),
                'createdAt': sync_record_data.get('created_at'),
                'completedAt': sync_record_data.get('completed_at'),
                'systemSource': 'ferry-system',
                'projectId': self.project_id
            }
            
            result = self._make_request('POST', self.endpoints['sync_records'], data=cmdb_data)
            
            self.sync_logger.info(f"CMDB同步记录创建成功: {result.get('id', 'unknown')}")
            return result
            
        except Exception as e:
            self.error_logger.error(f"创建CMDB同步记录失败: {str(e)}")
            # 不抛出异常，避免影响主流程
            return None
    
    def update_sync_record(self, cmdb_record_id, update_data):
        """更新CMDB中的同步记录"""
        try:
            self.sync_logger.info(f"更新CMDB同步记录: {cmdb_record_id}")
            
            endpoint = f"{self.endpoints['sync_records']}/{cmdb_record_id}"
            result = self._make_request('PUT', endpoint, data=update_data)
            
            self.sync_logger.info(f"CMDB同步记录更新成功: {cmdb_record_id}")
            return result
            
        except Exception as e:
            self.error_logger.error(f"更新CMDB同步记录失败: {str(e)}")
            return None
    
    def get_artifact_sync_status(self, artifact_id, target_env):
        """获取制品在目标环境的同步状态"""
        try:
            self.sync_logger.info(f"查询制品同步状态: {artifact_id} -> {target_env}")
            
            params = {
                'artifactId': artifact_id,
                'targetEnv': target_env,
                'recordType': 'sync_record'
            }
            
            result = self._make_request('GET', self.endpoints['sync_records'], params=params)
            
            # 检查是否有成功的同步记录
            if result and isinstance(result, list):
                for record in result:
                    if record.get('status') == 'success':
                        self.sync_logger.info(f"发现成功同步记录: {artifact_id} -> {target_env}")
                        return True
            
            self.sync_logger.info(f"未发现成功同步记录: {artifact_id} -> {target_env}")
            return False
            
        except Exception as e:
            self.error_logger.error(f"查询制品同步状态失败: {str(e)}")
            # 查询失败时返回False，触发重新同步
            return False
    
    def register_artifact(self, artifact_data):
        """在CMDB中注册制品信息"""
        try:
            self.sync_logger.info(f"注册CMDB制品: {artifact_data.get('artifact_id')}")
            
            cmdb_data = {
                'recordType': 'artifact',
                'artifactId': artifact_data.get('artifact_id'),
                'artifactName': artifact_data.get('name'),
                'version': artifact_data.get('version'),
                'versionId': artifact_data.get('version_id'),
                'status': artifact_data.get('status'),
                'description': artifact_data.get('description'),
                'fileSize': artifact_data.get('file_size'),
                'checksum': artifact_data.get('checksum'),
                'downloadUrl': artifact_data.get('download_url'),
                'environment': artifact_data.get('environment'),
                'createdAt': artifact_data.get('created_at'),
                'systemSource': 'ferry-system',
                'projectId': self.project_id
            }
            
            result = self._make_request('POST', self.endpoints['artifacts'], data=cmdb_data)
            
            self.sync_logger.info(f"CMDB制品注册成功: {result.get('id', 'unknown')}")
            return result
            
        except Exception as e:
            self.error_logger.error(f"注册CMDB制品失败: {str(e)}")
            return None
    
    def get_environment_artifacts(self, env_code):
        """获取环境中的制品列表"""
        try:
            self.sync_logger.info(f"获取环境制品列表: {env_code}")
            
            params = {
                'environment': env_code,
                'recordType': 'artifact'
            }
            
            result = self._make_request('GET', self.endpoints['artifacts'], params=params)
            
            self.sync_logger.info(f"获取环境制品列表成功: {env_code}, 数量: {len(result) if result else 0}")
            return result or []
            
        except Exception as e:
            self.error_logger.error(f"获取环境制品列表失败: {str(e)}")
            return []
    
    def check_artifact_exists_in_env(self, artifact_id, version, target_env):
        """检查制品版本是否已存在于目标环境"""
        try:
            self.sync_logger.info(f"检查制品是否存在: {artifact_id} v{version} in {target_env}")
            
            params = {
                'artifactId': artifact_id,
                'version': version,
                'environment': target_env,
                'recordType': 'artifact'
            }
            
            result = self._make_request('GET', self.endpoints['artifacts'], params=params)
            
            exists = bool(result and len(result) > 0)
            self.sync_logger.info(f"制品存在检查结果: {artifact_id} v{version} in {target_env} = {exists}")
            return exists
            
        except Exception as e:
            self.error_logger.error(f"检查制品存在性失败: {str(e)}")
            # 检查失败时返回False，触发重新同步
            return False
