class Config:
    """应用配置"""
    
    # Flask配置
    SECRET_KEY = 'dev-secret-key'

    # 数据库配置
    SQLALCHEMY_DATABASE_URI = 'sqlite:///ferry_system.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 制品仓库配置
    ARTIFACT_REPO_URL = 'http://116.204.72.136/next/contract-center'
    ARTIFACT_API_TOKEN = 'your-api-token'

    # CMDB配置
    CMDB_CONFIG = {
        'base_url': 'http://116.204.72.136/next/contract-center',
        'project_id': '60e0486c73912',
        'api_token': 'your-cmdb-token',
        'endpoints': {
            'sync_records': '/project/60e0486c73912/apis/cmdb/sync-records',
            'artifacts': '/project/60e0486c73912/apis/cmdb/artifacts',
            'environments': '/project/60e0486c73912/apis/cmdb/environments'
        }
    }
    
    # 环境配置
    ENVIRONMENTS = {
        'PROD_INTERNAL': {
            'name': '生产内网',
            'url': 'http://prod-internal.example.com',
            'token': 'prod-internal-token',
            'artifact_suffix': '_prod_internal'  # 制品名称后缀
        },
        'TEST_EXTERNAL': {
            'name': '测试外网',
            'url': 'http://test-external.example.com',
            'token': 'test-external-token',
            'artifact_suffix': '_test_external'  # 制品名称后缀
        },
        'TEST_INTERNAL': {
            'name': '测试内网',
            'url': 'http://test-internal.example.com',
            'token': 'test-internal-token',
            'artifact_suffix': '_test_internal'  # 制品名称后缀
        }
    }
    
    # 通知配置
    NOTIFICATION_CONFIG = {
        'email': {
            'smtp_server': 'smtp.example.com',
            'smtp_port': 587,
            'username': '<EMAIL>',
            'password': 'password',
            'recipients': ['<EMAIL>', '<EMAIL>']
        },
        'webhook': {
            'url': 'https://hooks.slack.com/services/xxx',
            'enabled': True
        }
    }
    
    # 同步配置
    SYNC_CONFIG = {
        'max_retries': 3,
        'retry_delay': 60,  # 秒
        'chunk_size': 8192,  # 下载块大小
        'timeout': 300,  # 超时时间
        'download_dir': '/tmp/artifacts',  # 下载目录
        'test_extract': True,  # 是否测试解压
        'supported_formats': ['.tar.gz', '.tar.bz2', '.tar.xz', '.zip', '.tar'],  # 支持的压缩格式
        'valid_statuses': ['预发布', '生产']  # 只扫描这些状态的制品
    }