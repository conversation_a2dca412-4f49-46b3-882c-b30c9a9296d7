#!/usr/bin/env python3
"""
项目加密打包脚本
"""

import os
import sys
import shutil
import subprocess
import tempfile
import base64
from pathlib import Path
from cryptography.fernet import Fernet
import zipfile

class ProjectBuilder:
    """项目构建器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.temp_dir = None
        self.encryption_key = None
        
    def generate_encryption_key(self):
        """生成加密密钥"""
        self.encryption_key = Fernet.generate_key()
        print(f"🔑 生成加密密钥: {self.encryption_key.decode()}")
        return self.encryption_key
    
    def encrypt_config_files(self):
        """加密配置文件"""
        print("🔐 加密配置文件...")
        
        if not self.encryption_key:
            self.generate_encryption_key()
        
        fernet = Fernet(self.encryption_key)
        
        # 需要加密的配置文件
        config_files = [
            'config.py',
            # 可以添加其他敏感文件
        ]
        
        encrypted_configs = {}
        
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                print(f"  加密: {config_file}")
                
                # 读取文件内容
                with open(config_path, 'rb') as f:
                    content = f.read()
                
                # 加密内容
                encrypted_content = fernet.encrypt(content)
                encrypted_configs[config_file] = base64.b64encode(encrypted_content).decode()
        
        return encrypted_configs
    
    def create_encrypted_loader(self, encrypted_configs):
        """创建加密配置加载器"""
        print("📝 创建配置加载器...")
        
        loader_code = f'''
import os
import base64
from cryptography.fernet import Fernet
import tempfile

class ConfigLoader:
    """加密配置加载器"""
    
    def __init__(self):
        # 从环境变量获取密钥
        key = os.environ.get('FERRY_ENCRYPTION_KEY')
        if not key:
            raise Exception("未找到加密密钥，请设置环境变量 FERRY_ENCRYPTION_KEY")
        
        self.fernet = Fernet(key.encode())
        self.temp_files = []
    
    def load_config(self, config_name):
        """加载加密的配置文件"""
        encrypted_configs = {encrypted_configs}
        
        if config_name not in encrypted_configs:
            raise Exception(f"配置文件不存在: {{config_name}}")
        
        # 解密配置
        encrypted_data = base64.b64decode(encrypted_configs[config_name])
        decrypted_content = self.fernet.decrypt(encrypted_data)
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False)
        temp_file.write(decrypted_content.decode())
        temp_file.close()
        
        self.temp_files.append(temp_file.name)
        return temp_file.name
    
    def cleanup(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass

# 全局配置加载器
config_loader = ConfigLoader()
'''
        
        # 写入加载器文件
        loader_path = self.project_root / "encrypted_loader.py"
        with open(loader_path, 'w', encoding='utf-8') as f:
            f.write(loader_code)
        
        return loader_path
    
    def modify_main_app(self):
        """修改主应用以使用加密配置"""
        print("🔧 修改主应用...")
        
        # 备份原始app.py
        app_path = self.project_root / "app.py"
        backup_path = self.project_root / "app_original.py"
        shutil.copy2(app_path, backup_path)
        
        # 读取原始内容
        with open(app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修改导入语句
        modified_content = content.replace(
            "from config import Config",
            """
# 使用加密配置
import sys
import os
from encrypted_loader import config_loader

# 加载加密的配置文件
config_path = config_loader.load_config('config.py')
config_dir = os.path.dirname(config_path)
config_name = os.path.basename(config_path).replace('.py', '')

# 添加到Python路径
if config_dir not in sys.path:
    sys.path.insert(0, config_dir)

# 动态导入配置
import importlib.util
spec = importlib.util.spec_from_file_location("config", config_path)
config_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(config_module)
Config = config_module.Config
"""
        )
        
        # 写入修改后的内容
        with open(app_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        return backup_path
    
    def create_pyinstaller_spec(self):
        """创建PyInstaller规格文件"""
        print("📋 创建PyInstaller规格文件...")
        
        spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('services', 'services'),
    ],
    hiddenimports=[
        'flask',
        'flask_sqlalchemy',
        'requests',
        'cryptography',
        'flasgger',
        'logging.handlers',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ferry_system',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
        
        spec_path = self.project_root / "ferry_system.spec"
        with open(spec_path, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        return spec_path
    
    def build_executable(self):
        """构建可执行文件"""
        print("🔨 构建可执行文件...")
        
        try:
            # 运行PyInstaller
            result = subprocess.run([
                sys.executable, '-m', 'PyInstaller',
                '--clean',
                '--onefile',
                'ferry_system.spec'
            ], check=True, capture_output=True, text=True)
            
            print("✅ 构建成功!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 构建失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def create_deployment_package(self):
        """创建部署包"""
        print("📦 创建部署包...")
        
        # 创建部署目录
        deploy_dir = self.project_root / "deploy"
        deploy_dir.mkdir(exist_ok=True)
        
        # 复制可执行文件
        exe_path = self.dist_dir / "ferry_system"
        if exe_path.exists():
            shutil.copy2(exe_path, deploy_dir / "ferry_system")
        
        # 创建启动脚本
        start_script = deploy_dir / "start.sh"
        with open(start_script, 'w') as f:
            f.write(f'''#!/bin/bash
# 摆渡机系统启动脚本

# 设置加密密钥
export FERRY_ENCRYPTION_KEY="{self.encryption_key.decode()}"

# 创建必要目录
mkdir -p data logs

# 启动应用
./ferry_system
''')
        
        # 设置执行权限
        os.chmod(start_script, 0o755)
        
        # 创建systemd服务文件
        service_file = deploy_dir / "ferry-system.service"
        with open(service_file, 'w') as f:
            f.write('''[Unit]
Description=Ferry System - Artifact Sync Service
After=network.target

[Service]
Type=simple
User=ferry
WorkingDirectory=/opt/ferry-system
Environment=FERRY_ENCRYPTION_KEY=YOUR_ENCRYPTION_KEY_HERE
ExecStart=/opt/ferry-system/ferry_system
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
''')
        
        # 创建部署说明
        readme = deploy_dir / "DEPLOY_README.md"
        with open(readme, 'w') as f:
            f.write(f'''# 摆渡机系统部署说明

## 加密密钥
```
{self.encryption_key.decode()}
```

## 部署步骤

1. 上传文件到服务器
```bash
scp -r deploy/ user@server:/tmp/ferry-deploy/
```

2. 在服务器上安装
```bash
sudo mkdir -p /opt/ferry-system
sudo cp /tmp/ferry-deploy/* /opt/ferry-system/
sudo chmod +x /opt/ferry-system/ferry_system
sudo chmod +x /opt/ferry-system/start.sh
```

3. 创建用户
```bash
sudo useradd -r -s /bin/false ferry
sudo chown -R ferry:ferry /opt/ferry-system
```

4. 安装systemd服务
```bash
sudo cp /opt/ferry-system/ferry-system.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable ferry-system
sudo systemctl start ferry-system
```

5. 检查状态
```bash
sudo systemctl status ferry-system
sudo journalctl -u ferry-system -f
```

## 注意事项
- 请妥善保管加密密钥
- 确保服务器有足够的磁盘空间
- 定期备份数据目录
''')
        
        print(f"✅ 部署包创建完成: {deploy_dir}")
        return deploy_dir
    
    def cleanup(self):
        """清理临时文件"""
        print("🧹 清理临时文件...")
        
        # 恢复原始app.py
        app_path = self.project_root / "app.py"
        backup_path = self.project_root / "app_original.py"
        
        if backup_path.exists():
            shutil.move(backup_path, app_path)
        
        # 删除临时文件
        temp_files = [
            "encrypted_loader.py",
            "ferry_system.spec",
        ]
        
        for temp_file in temp_files:
            temp_path = self.project_root / temp_file
            if temp_path.exists():
                temp_path.unlink()
        
        # 清理构建目录
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
    
    def build(self):
        """完整构建流程"""
        print("🚀 开始项目加密打包")
        print("=" * 50)
        
        try:
            # 1. 生成加密密钥
            self.generate_encryption_key()
            
            # 2. 加密配置文件
            encrypted_configs = self.encrypt_config_files()
            
            # 3. 创建配置加载器
            self.create_encrypted_loader(encrypted_configs)
            
            # 4. 修改主应用
            backup_path = self.modify_main_app()
            
            # 5. 创建PyInstaller规格
            self.create_pyinstaller_spec()
            
            # 6. 构建可执行文件
            if not self.build_executable():
                return False
            
            # 7. 创建部署包
            deploy_dir = self.create_deployment_package()
            
            print("\n🎉 构建完成!")
            print(f"部署包位置: {deploy_dir}")
            print(f"加密密钥: {self.encryption_key.decode()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 构建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 清理临时文件
            self.cleanup()

def main():
    """主函数"""
    print("🔐 摆渡机系统加密打包工具")
    print("=" * 40)
    
    # 检查依赖
    try:
        import PyInstaller
        from cryptography.fernet import Fernet
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install pyinstaller cryptography")
        return
    
    # 确认操作
    choice = input("是否开始构建加密版本? (y/n): ").strip().lower()
    if choice not in ['y', 'yes']:
        print("❌ 用户取消操作")
        return
    
    # 开始构建
    builder = ProjectBuilder()
    success = builder.build()
    
    if success:
        print("\n📋 下一步:")
        print("1. 查看 deploy/ 目录中的部署文件")
        print("2. 阅读 DEPLOY_README.md 了解部署步骤")
        print("3. 上传到Linux服务器并按说明部署")
    else:
        print("\n❌ 构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
