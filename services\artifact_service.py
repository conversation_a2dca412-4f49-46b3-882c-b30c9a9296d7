import requests
import hashlib
import os
import logging
from datetime import datetime
from models import db, Artifact
from config import Config

class ArtifactService:
    """制品服务"""
    
    def __init__(self):
        self.config = Config()
        self.api_url = self.config.ARTIFACT_REPO_URL
        self.api_token = self.config.ARTIFACT_API_TOKEN
        self.logger = logging.getLogger(__name__)
        self.sync_logger = logging.getLogger('sync')
        self.error_logger = logging.getLogger('error')
    
    def get_artifacts(self, status=None):
        """获取制品列表"""
        query = Artifact.query
        if status:
            query = query.filter(Artifact.status == status)
        artifacts = query.order_by(Artifact.created_at.desc()).all()
        self.logger.info(f"获取制品列表: 状态={status}, 数量={len(artifacts)}")
        return artifacts
    
    def get_artifact(self, artifact_id):
        """获取单个制品"""
        artifact = Artifact.query.filter_by(artifact_id=artifact_id).first()
        if artifact:
            self.logger.info(f"获取制品: {artifact_id}")
        else:
            self.logger.warning(f"制品不存在: {artifact_id}")
        return artifact
    
    def create_artifact(self, data):
        """创建制品记录"""
        try:
            artifact = Artifact(
                name=data['name'],
                artifact_id=data['artifact_id'],
                version=data['version'],
                version_id=data['version_id'],
                status=data['status'],
                description=data.get('description', ''),
                file_size=data.get('file_size', 0),
                checksum=data.get('checksum', ''),
                download_url=data.get('download_url', ''),
                environment=data.get('environment', '')
            )
            db.session.add(artifact)
            db.session.commit()
            
            self.sync_logger.info(f"创建制品记录: {artifact.artifact_id} v{artifact.version} "
                                f"({artifact.name}), 状态: {artifact.status}, "
                                f"大小: {artifact.file_size} bytes")
            return artifact
        except Exception as e:
            self.error_logger.error(f"创建制品记录失败: {str(e)}, 数据: {data}")
            raise
    
    def check_new_versions(self):
        """检查新版本（模拟API调用）"""
        try:
            self.sync_logger.info(f"开始检查新版本: {self.api_url}/project/60e0486c73912/apis/635f69a7b27f5")
            
            # 模拟API调用获取新版本
            headers = {'Authorization': f'Bearer {self.api_token}'}
            response = requests.get(
                f'{self.api_url}/project/60e0486c73912/apis/635f69a7b27f5',
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                self.sync_logger.info("API调用成功，解析版本数据")
                
                # 模拟返回数据
                mock_data = [
                    {
                        'artifact_id': 'percat_UAT_001',
                        'name': 'percat_UAT',
                        'version': '1.2.3',
                        'version_id': 'v123',
                        'status': '预发布',
                        'description': '新功能发布',
                        'download_url': f'{self.api_url}/download/percat_UAT_001',
                        'file_size': 1024000,
                        'checksum': 'abc123def456',
                        'environment': 'UAT'
                    },
                    {
                        'artifact_id': 'percat_PROD_001',
                        'name': 'percat_PROD',
                        'version': '1.2.2',
                        'version_id': 'v122',
                        'status': '生产',
                        'description': '生产环境发布',
                        'download_url': f'{self.api_url}/download/percat_PROD_001',
                        'file_size': 1024000,
                        'checksum': 'def456ghi789',
                        'environment': 'PROD'
                    }
                ]
                
                self.sync_logger.info(f"检查新版本完成，发现 {len(mock_data)} 个版本")
                for data in mock_data:
                    self.sync_logger.debug(f"版本信息: {data['artifact_id']} v{data['version']} - {data['status']}")
                
                return mock_data
            else:
                error_msg = f"API调用失败: HTTP {response.status_code}"
                self.error_logger.error(error_msg)
                raise Exception(error_msg)
                
        except Exception as e:
            error_msg = f"检查新版本失败: {str(e)}"
            self.error_logger.error(error_msg)
            raise Exception(error_msg)
    
    def download_artifact(self, download_url, local_path):
        """下载制品文件"""
        try:
            self.sync_logger.info(f"开始下载制品: {download_url}")
            
            headers = {'Authorization': f'Bearer {self.api_token}'}
            response = requests.get(download_url, headers=headers, stream=True, timeout=300)
            response.raise_for_status()
            
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            downloaded_size = 0
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    downloaded_size += len(chunk)
            
            self.sync_logger.info(f"下载完成: {local_path}, 大小: {downloaded_size} bytes")
            return True
            
        except Exception as e:
            error_msg = f"下载制品失败: {str(e)}"
            self.error_logger.error(f"{error_msg}, URL: {download_url}")
            raise Exception(error_msg)
    
    def verify_checksum(self, file_path, expected_checksum):
        """验证文件校验和"""
        try:
            self.sync_logger.info(f"开始校验文件: {file_path}")
            
            sha256_hash = hashlib.sha256()
            file_size = 0
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
                    file_size += len(chunk)
            
            actual_checksum = sha256_hash.hexdigest()
            
            if actual_checksum == expected_checksum:
                self.sync_logger.info(f"文件校验成功: {file_path}, 大小: {file_size} bytes, "
                                    f"校验和: {actual_checksum}")
                return True
            else:
                self.error_logger.error(f"文件校验失败: {file_path}, "
                                      f"期望: {expected_checksum}, 实际: {actual_checksum}")
                return False
                
        except Exception as e:
            error_msg = f"校验文件失败: {str(e)}"
            self.error_logger.error(f"{error_msg}, 文件: {file_path}")
            raise Exception(error_msg)
