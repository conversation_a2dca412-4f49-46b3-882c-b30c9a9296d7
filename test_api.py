#!/usr/bin/env python3
"""
API接口测试脚本
"""

import requests
import json
from datetime import datetime

# 基础URL
BASE_URL = "http://localhost:5000"

def test_health():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"错误: {e}")
    print()

def test_sync_by_date():
    """测试指定时间同步制品接口"""
    print("=== 测试指定时间同步制品接口 ===")
    try:
        # 测试正确的日期格式
        date_str = "20240115"
        response = requests.get(f"{BASE_URL}/uniform/api/v1/sync/artifact?date={date_str}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 测试错误的日期格式
        print("\n--- 测试错误日期格式 ---")
        response = requests.get(f"{BASE_URL}/uniform/api/v1/sync/artifact?date=invalid")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 测试缺少参数
        print("\n--- 测试缺少参数 ---")
        response = requests.get(f"{BASE_URL}/uniform/api/v1/sync/artifact")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"错误: {e}")
    print()

def test_sync_by_name():
    """测试指定程序包名称同步制品接口"""
    print("=== 测试指定程序包名称同步制品接口 ===")
    try:
        # 测试正确的程序包名称
        artifact_name = "percat_UAT"
        response = requests.get(f"{BASE_URL}/uniform/api/v1/sync/artifact/by-name?artifactName={artifact_name}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 测试缺少参数
        print("\n--- 测试缺少参数 ---")
        response = requests.get(f"{BASE_URL}/uniform/api/v1/sync/artifact/by-name")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"错误: {e}")
    print()

def test_get_sync_records():
    """测试指定时间查询同步记录接口"""
    print("=== 测试指定时间查询同步记录接口 ===")
    try:
        # 测试正确的日期格式
        date_str = "20240115"
        response = requests.get(f"{BASE_URL}/uniform/api/v1/sync/records?date={date_str}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        # 测试缺少参数
        print("\n--- 测试缺少参数 ---")
        response = requests.get(f"{BASE_URL}/uniform/api/v1/sync/records")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"错误: {e}")
    print()

def test_create_artifact():
    """测试创建制品记录接口"""
    print("=== 测试创建制品记录接口 ===")
    try:
        data = {
            "name": "test_artifact",
            "artifact_id": "test_001",
            "version": "1.0.0",
            "version_id": "v100",
            "status": "预发布",
            "description": "测试制品",
            "file_size": 1024,
            "checksum": "test_checksum",
            "download_url": "http://example.com/download/test_001",
            "environment": "TEST"
        }
        
        response = requests.post(f"{BASE_URL}/api/artifacts", json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"错误: {e}")
    print()

def test_get_artifacts():
    """测试获取制品列表接口"""
    print("=== 测试获取制品列表接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/api/artifacts")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"错误: {e}")
    print()

if __name__ == "__main__":
    print("开始测试API接口...")
    print(f"当前时间: {datetime.now()}")
    print(f"基础URL: {BASE_URL}")
    print("=" * 50)
    
    # 运行所有测试
    test_health()
    test_create_artifact()
    test_get_artifacts()
    test_sync_by_date()
    test_sync_by_name()
    test_get_sync_records()
    
    print("测试完成!")
