# 摆渡机制品同步系统

基于Flask的制品同步系统，用于在不同环境间自动同步制品包。

## 功能特性

1. **制品管理**: 管理制品包的版本信息和状态
2. **自动同步**: 每5分钟检查新版本并自动同步
3. **手动同步**: 支持手动触发同步指定制品
4. **按日期同步**: 支持同步指定日期的制品
5. **完整性校验**: 下载后验证文件完整性
6. **重试机制**: 失败后自动重试
7. **通知系统**: 邮件和Webhook通知
8. **同步记录**: 完整的同步历史记录

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```


### 运行应用

```bash
python app.py
```

## API接口

### 📖 Swagger文档

本项目集成了Swagger API文档，提供美观的交互式接口文档：

- **Swagger UI**: http://localhost:5000/api/docs/
- **API规范**: http://localhost:5000/apispec.json

#### 快速体验
```bash
# 安装Swagger依赖
python install_swagger.py

# 启动应用
python app.py

# 或使用演示脚本
python demo_swagger.py
```

### 制品管理

- `GET /api/artifacts` - 获取制品列表
- `GET /api/artifacts/<id>` - 获取制品详情
- `POST /api/artifacts` - 创建制品记录

### 同步管理

- `POST /api/sync` - 手动触发同步
- `GET /api/sync` - 获取同步记录

### 统一接口

- `GET /uniform/api/v1/sync/artifact` - 指定时间同步制品
- `GET /uniform/api/v1/sync/artifact/by-name` - 指定程序包名称同步制品
- `GET /uniform/api/v1/sync/records` - 指定时间查询同步记录

### 系统监控

- `GET /api/health` - 健康检查

## 配置说明

在 `config.py` 中配置：

- 数据库连接
- 制品仓库API
- 环境信息
- 通知配置
- 同步参数

## 部署建议

1. 使用生产级数据库（PostgreSQL/MySQL）
2. 配置日志轮转
3. 设置监控告警
4. 定期备份数据库
5. 配置负载均衡