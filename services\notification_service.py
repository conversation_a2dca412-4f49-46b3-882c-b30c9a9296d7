import smtplib
import requests
import json
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from datetime import datetime
from config import Config

class NotificationService:
    """通知服务"""
    
    def __init__(self):
        self.config = Config()
        self.notification_config = self.config.NOTIFICATION_CONFIG
        self.logger = logging.getLogger(__name__)
        self.sync_logger = logging.getLogger('sync')
        self.error_logger = logging.getLogger('error')
    
    def send_sync_failure_notification(self, artifact, target_env, error_message):
        """发送同步失败通知"""
        subject = f"制品同步失败 - {artifact.name} v{artifact.version}"
        message = f"""
制品同步失败通知

制品信息:
- 制品名称: {artifact.name}
- 版本: {artifact.version}
- 制品ID: {artifact.artifact_id}
- 目标环境: {target_env}

错误信息:
{error_message}

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

请及时处理此问题。
        """
        
        self.sync_logger.error(f"发送同步失败通知: {artifact.artifact_id} v{artifact.version} -> {target_env}")
        self._send_email(subject, message)
        self._send_webhook(subject, message)
    
    def send_error_notification(self, error_message):
        """发送一般错误通知"""
        subject = "摆渡机系统错误"
        message = f"""
系统错误通知

错误信息: {error_message}
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

请检查系统状态。
        """
        
        self.error_logger.error(f"发送系统错误通知: {error_message}")
        self._send_email(subject, message)
        self._send_webhook(subject, message)
    
    def send_sync_success_notification(self, artifact, target_env):
        """发送同步成功通知"""
        subject = f"制品同步成功 - {artifact.name} v{artifact.version}"
        message = f"""
制品同步成功通知

制品信息:
- 制品名称: {artifact.name}
- 版本: {artifact.version}
- 制品ID: {artifact.artifact_id}
- 目标环境: {target_env}

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        self.sync_logger.info(f"发送同步成功通知: {artifact.artifact_id} v{artifact.version} -> {target_env}")
        self._send_webhook(subject, message)
    
    def _send_email(self, subject, message):
        """发送邮件通知"""
        try:
            email_config = self.notification_config['email']
            
            msg = MIMEMultipart()
            msg['From'] = email_config['username']
            msg['To'] = ', '.join(email_config['recipients'])
            msg['Subject'] = subject
            
            msg.attach(MIMEText(message, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            
            text = msg.as_string()
            server.sendmail(email_config['username'], email_config['recipients'], text)
            server.quit()
            
            self.logger.info(f"邮件通知发送成功: {subject}")
            
        except Exception as e:
            self.error_logger.error(f"发送邮件失败: {str(e)}, 主题: {subject}")
    
    def _send_webhook(self, subject, message):
        """发送Webhook通知"""
        try:
            webhook_config = self.notification_config['webhook']
            if not webhook_config.get('enabled'):
                self.logger.debug("Webhook通知已禁用")
                return
            
            payload = {
                'text': f"*{subject}*\n```{message}```",
                'timestamp': datetime.now().isoformat()
            }
            
            response = requests.post(
                webhook_config['url'],
                json=payload,
                timeout=10
            )
            response.raise_for_status()
            
            self.logger.info(f"Webhook通知发送成功: {subject}")
            
        except Exception as e:
            self.error_logger.error(f"发送Webhook通知失败: {str(e)}, 主题: {subject}")
