# 摆渡机制品同步系统 API 接口文档

## 系统概述

摆渡机制品同步系统是基于Flask的制品同步系统，用于在不同环境间自动同步制品包。系统提供完整的制品管理、同步管理和监控功能。

## 基本信息

- **服务地址**: `http://localhost:5000`
- **API版本**: v1.0.0
- **Content-Type**: `application/json`
- **数据库**: SQLite (开发环境) / PostgreSQL (生产环境)

## 接口概览

| 分类 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 制品管理 | GET | `/api/artifacts` | 获取制品列表 |
| 制品管理 | GET | `/api/artifacts/{artifact_id}` | 获取制品详情 |
| 制品管理 | POST | `/api/artifacts` | 创建制品记录 |
| 同步管理 | POST | `/api/sync` | 手动触发同步 |
| 同步管理 | GET | `/api/sync` | 获取同步记录 |
| 统一接口 | GET | `/uniform/api/v1/sync/artifact` | 指定时间同步制品 |
| 统一接口 | GET | `/uniform/api/v1/sync/artifact/by-name` | 指定程序包名称同步制品 |
| 统一接口 | GET | `/uniform/api/v1/sync/records` | 指定时间查询同步记录 |
| 系统监控 | GET | `/api/health` | 健康检查 |

---

## 数据模型

### 制品模型 (Artifact)

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Integer | 是 | 主键ID |
| name | String(255) | 是 | 制品包名称 |
| artifact_id | String(100) | 是 | 制品包ID（唯一） |
| version | String(100) | 是 | 版本名称 |
| version_id | String(100) | 是 | 版本ID |
| status | String(50) | 是 | 状态（预发布、生产） |
| description | Text | 否 | 版本备注 |
| file_size | BigInteger | 否 | 文件大小（字节） |
| checksum | String(64) | 否 | 文件校验和 |
| download_url | String(500) | 否 | 下载地址 |
| environment | String(50) | 否 | 环境标识 |
| created_at | DateTime | 是 | 创建时间 |
| updated_at | DateTime | 是 | 更新时间 |

### 同步记录模型 (SyncRecord)

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Integer | 是 | 主键ID |
| artifact_id | String(100) | 是 | 制品包ID |
| artifact_name | String(255) | 是 | 制品包名称 |
| version | String(100) | 是 | 版本名称 |
| source_env | String(50) | 是 | 源环境 |
| target_env | String(50) | 是 | 目标环境 |
| status | String(50) | 是 | 同步状态（success, failed, in_progress） |
| error_message | Text | 否 | 错误信息 |
| retry_count | Integer | 是 | 重试次数 |
| sync_type | String(50) | 是 | 同步类型（auto, manual） |
| created_at | DateTime | 是 | 创建时间 |
| completed_at | DateTime | 否 | 完成时间 |

---

## 制品管理接口

### 1. 获取制品列表

**接口地址**: `GET /api/artifacts`

**描述**: 获取所有制品列表，支持按状态过滤

**请求参数**:
- `status` (可选): 制品状态过滤，如 "预发布"、"生产"

**请求示例**:
```bash
GET /api/artifacts
GET /api/artifacts?status=预发布
```

**响应示例**:
```json
[
  {
    "id": 1,
    "name": "percat_UAT",
    "artifact_id": "percat_UAT_001",
    "version": "1.2.3",
    "version_id": "v123",
    "status": "预发布",
    "description": "新功能发布",
    "file_size": 1024000,
    "checksum": "abc123def456",
    "download_url": "http://**************/next/contract-center/download/percat_UAT_001",
    "environment": "UAT",
    "created_at": "2024-01-15T10:30:00",
    "updated_at": "2024-01-15T10:30:00"
  }
]
```

### 2. 获取制品详情

**接口地址**: `GET /api/artifacts/{artifact_id}`

**描述**: 根据制品ID获取详细信息

**路径参数**:
- `artifact_id`: 制品ID

**请求示例**:
```bash
GET /api/artifacts/1
```

**响应示例**:
```json
{
  "id": 1,
  "name": "percat_UAT",
  "artifact_id": "percat_UAT_001",
  "version": "1.2.3",
  "version_id": "v123",
  "status": "预发布",
  "description": "新功能发布",
  "file_size": 1024000,
  "checksum": "abc123def456",
  "download_url": "http://**************/next/contract-center/download/percat_UAT_001",
  "environment": "UAT",
  "created_at": "2024-01-15T10:30:00",
  "updated_at": "2024-01-15T10:30:00"
}
```

**错误响应**:
```json
{
  "error": "制品不存在"
}
```

### 3. 创建制品记录

**接口地址**: `POST /api/artifacts`

**描述**: 创建新的制品记录

**请求体**:
```json
{
  "name": "percat_UAT",
  "artifact_id": "percat_UAT_001",
  "version": "1.2.3",
  "version_id": "v123",
  "status": "预发布",
  "description": "新功能发布",
  "file_size": 1024000,
  "checksum": "abc123def456",
  "download_url": "http://**************/next/contract-center/download/percat_UAT_001",
  "environment": "UAT"
}
```

**必填字段**:
- `name`: 制品包名称
- `artifact_id`: 制品包ID（唯一）
- `version`: 版本名称
- `version_id`: 版本ID
- `status`: 状态（预发布、生产）

**可选字段**:
- `description`: 版本备注
- `file_size`: 文件大小（字节）
- `checksum`: 文件校验和
- `download_url`: 下载地址
- `environment`: 环境标识

**响应示例**:
```json
{
  "id": 1,
  "name": "percat_UAT",
  "artifact_id": "percat_UAT_001",
  "version": "1.2.3",
  "version_id": "v123",
  "status": "预发布",
  "description": "新功能发布",
  "file_size": 1024000,
  "checksum": "abc123def456",
  "download_url": "http://**************/next/contract-center/download/percat_UAT_001",
  "environment": "UAT",
  "created_at": "2024-01-15T10:30:00",
  "updated_at": "2024-01-15T10:30:00"
}
```

---

## 同步管理接口

### 1. 手动触发同步

**接口地址**: `POST /api/sync`

**描述**: 手动触发指定制品的同步

**请求体**:
```json
{
  "artifact_id": "percat_UAT_001",
  "target_env": "TEST_EXTERNAL"
}
```

**请求参数**:
- `artifact_id`: 制品ID
- `target_env`: 目标环境（PROD_INTERNAL、TEST_EXTERNAL、TEST_INTERNAL）

**响应示例**:
```json
{
  "status": "success",
  "artifact_id": "percat_UAT_001",
  "target_env": "TEST_EXTERNAL",
  "sync_record_id": 123,
  "duration": 45.67
}
```

**错误响应**:
```json
{
  "error": "制品不存在: percat_UAT_001"
}
```

### 2. 获取同步记录

**接口地址**: `GET /api/sync`

**描述**: 获取最近50条同步记录

**响应示例**:
```json
[
  {
    "id": 123,
    "artifact_id": "percat_UAT_001",
    "artifact_name": "percat_UAT",
    "version": "1.2.3",
    "source_env": "UAT",
    "target_env": "TEST_EXTERNAL",
    "status": "success",
    "error_message": null,
    "retry_count": 0,
    "sync_type": "manual",
    "created_at": "2024-01-15T10:30:00",
    "completed_at": "2024-01-15T10:31:00"
  }
]
```

---

## 统一接口（符合规范格式）

### 1. 指定时间同步制品

**接口地址**: `GET /uniform/api/v1/sync/artifact`

**描述**: 根据指定时间同步所有当天更新的制品，不仅检查生产内网的CMDB模型配置，同时强制检查目标环境是否有对应版本，若发现对应目录环境没有对应版本，重新同步制品并更新生产内网的CMDB模型配置

**请求参数**:
- `date`: 日期（YYYYMMDD格式，如：20150721）

**请求示例**:
```bash
GET /uniform/api/v1/sync/artifact?date=20150721
```

**响应示例**:
```json
{
  "code": 0,
  "error": "",
  "message": "同步完成",
  "data": [
    {
      "artifactName": "percat_UAT",
      "artifactId": "percat_UAT_001",
      "versionName": "1.2.3",
      "versionId": "v123",
      "memo": "新功能发布",
      "date": "2024-01-15",
      "status": "success",
      "targetEnv": "TEST_EXTERNAL"
    }
  ]
}
```

**错误响应**:
```json
{
  "code": 100000,
  "error": "参数结构错误",
  "message": "缺少date参数",
  "data": null
}
```

### 2. 指定程序包名称同步制品

**接口地址**: `GET /uniform/api/v1/sync/artifact/by-name`

**描述**: 根据程序包名称同步指定制品的所有版本

**请求参数**:
- `artifactName`: 程序包名称

**请求示例**:
```bash
GET /uniform/api/v1/sync/artifact/by-name?artifactName=percat_UAT
```

**响应示例**:
```json
{
  "code": 0,
  "error": "",
  "message": "同步完成",
  "data": [
    {
      "artifactName": "percat_UAT",
      "artifactId": "percat_UAT_001",
      "versionName": "1.2.3",
      "versionId": "v123",
      "memo": "新功能发布",
      "date": "2024-01-15",
      "status": "success",
      "targetEnv": "TEST_EXTERNAL"
    }
  ]
}
```

### 3. 指定时间查询同步记录

**接口地址**: `GET /uniform/api/v1/sync/records`

**描述**: 查询指定日期的所有同步记录

**请求参数**:
- `date`: 日期（YYYYMMDD格式，如：20150721）

**请求示例**:
```bash
GET /uniform/api/v1/sync/records?date=20150721
```

**响应示例**:
```json
{
  "code": 0,
  "error": "",
  "message": "查询完成",
  "data": [
    {
      "syncId": 123,
      "artifactName": "percat_UAT",
      "artifactId": "percat_UAT_001",
      "versionName": "1.2.3",
      "sourceEnv": "UAT",
      "targetEnv": "TEST_EXTERNAL",
      "status": "success",
      "syncType": "manual",
      "errorMessage": "",
      "retryCount": 0,
      "createdAt": "2024-01-15 10:30:00",
      "completedAt": "2024-01-15 10:31:00"
    }
  ]
}
```

---

## 系统监控接口

### 健康检查

**接口地址**: `GET /api/health`

**描述**: 检查系统健康状态

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.123456",
  "version": "1.0.0"
}
```

---

## 错误码说明

### HTTP状态码
| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 业务状态码（统一接口）
| 业务状态码 | 说明 |
|------------|------|
| 0 | 成功 |
| 100000 | 参数结构错误 |
| 100003 | 加密认证失败 |
| 100005 | url地址找不到 |
| 133503 | 参数数据类型校验不通过 |

---

## 环境配置

### 支持的目标环境
- `PROD_INTERNAL`: 生产内网
- `TEST_EXTERNAL`: 测试外网
- `TEST_INTERNAL`: 测试内网

### 制品状态说明
- `预发布`: 预发布版本
- `生产`: 生产版本

### 同步状态说明
- `success`: 同步成功
- `failed`: 同步失败
- `in_progress`: 同步进行中

### 同步类型说明
- `auto`: 自动同步（定时任务触发）
- `manual`: 手动同步（用户触发）

---

## 系统特性

### 自动同步机制
- 系统每5分钟自动检查新版本
- 根据制品状态自动确定目标环境
- 支持失败重试（最多3次）

### 完整性校验
- 下载后验证文件SHA256校验和
- 确保文件传输完整性

### 日志记录
- 详细的同步日志记录
- 分类日志存储（同步日志、错误日志）
- 支持日志轮转

### 通知系统
- 邮件通知
- Webhook通知（如Slack）
- 同步成功/失败通知

---

## 使用示例

### 创建制品并触发同步
```bash
# 1. 创建制品记录
curl -X POST http://localhost:5000/api/artifacts \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my_app",
    "artifact_id": "my_app_001",
    "version": "1.0.0",
    "version_id": "v100",
    "status": "预发布"
  }'

# 2. 手动触发同步
curl -X POST http://localhost:5000/api/sync \
  -H "Content-Type: application/json" \
  -d '{
    "artifact_id": "my_app_001",
    "target_env": "TEST_EXTERNAL"
  }'

# 3. 查看同步记录
curl http://localhost:5000/api/sync
```

### 使用统一接口
```bash
# 按日期同步制品
curl "http://localhost:5000/uniform/api/v1/sync/artifact?date=20240115"

# 按程序包名称同步
curl "http://localhost:5000/uniform/api/v1/sync/artifact/by-name?artifactName=my_app"

# 查询同步记录
curl "http://localhost:5000/uniform/api/v1/sync/records?date=20240115"
```
