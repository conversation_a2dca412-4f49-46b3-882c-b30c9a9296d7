#!/usr/bin/env python3
"""
安装构建依赖的脚本
"""

import subprocess
import sys
import os

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_package(package):
    """安装Python包"""
    try:
        print(f"📦 安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package,
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
        ], check=True, capture_output=True, text=True)
        
        print(f"✅ {package} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package_installed(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🔧 摆渡机系统构建依赖安装")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 需要安装的包
    required_packages = [
        "pyinstaller",
        "cryptography", 
        "requests",
        "flask",
        "flask-sqlalchemy",
        "flasgger"
    ]
    
    print(f"\n📋 需要安装的包: {len(required_packages)} 个")
    for pkg in required_packages:
        print(f"  - {pkg}")
    
    # 确认安装
    choice = input("\n是否继续安装? (y/n): ").strip().lower()
    if choice not in ['y', 'yes']:
        print("❌ 用户取消安装")
        return False
    
    # 升级pip
    print("\n🔄 升级pip...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip",
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
        ], check=True)
        print("✅ pip升级成功")
    except:
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装包
    print("\n📦 开始安装依赖包...")
    success_count = 0
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    # 验证安装
    print(f"\n🔍 验证安装结果...")
    verification_map = {
        "pyinstaller": "PyInstaller",
        "cryptography": "cryptography", 
        "requests": "requests",
        "flask": "flask",
        "flask-sqlalchemy": "flask_sqlalchemy",
        "flasgger": "flasgger"
    }
    
    verified_count = 0
    for package, import_name in verification_map.items():
        if check_package_installed(import_name):
            print(f"✅ {package} 验证成功")
            verified_count += 1
        else:
            print(f"❌ {package} 验证失败")
    
    # 总结
    print("\n" + "=" * 40)
    print(f"📊 安装结果:")
    print(f"  成功安装: {success_count}/{len(required_packages)}")
    print(f"  验证通过: {verified_count}/{len(required_packages)}")
    
    if verified_count == len(required_packages):
        print("\n🎉 所有依赖安装成功!")
        print("\n📋 下一步:")
        print("1. 运行: python build_encrypted.py")
        print("2. 构建加密版本的应用")
        print("3. 部署到Linux服务器")
        return True
    else:
        print("\n❌ 部分依赖安装失败")
        print("请检查错误信息并手动安装失败的包")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
