# 摆渡机系统加密部署指南

## 概述

本指南将帮助你将摆渡机系统进行加密打包并部署到Linux服务器上。

## 🔐 加密方案

### 加密内容
- **配置文件**: config.py 等敏感配置
- **源代码**: 打包为二进制可执行文件
- **运行时解密**: 使用环境变量中的密钥动态解密

### 安全特性
- ✅ 源代码完全隐藏
- ✅ 配置文件加密存储
- ✅ 密钥与程序分离
- ✅ 运行时动态解密

## 📋 部署步骤

### 第一步: 准备开发环境

#### 1.1 安装构建依赖
```bash
# 安装构建所需的Python包
python install_build_deps.py
```

#### 1.2 验证环境
```bash
# 检查Python版本 (需要3.7+)
python --version

# 检查依赖包
python -c "import PyInstaller, cryptography; print('依赖检查通过')"
```

### 第二步: 构建加密版本

#### 2.1 运行构建脚本
```bash
# 构建加密的可执行文件
python build_encrypted.py
```

#### 2.2 构建过程说明
构建脚本会自动完成以下步骤：
1. 生成随机加密密钥
2. 加密配置文件 (config.py)
3. 创建配置解密器
4. 修改主应用以使用加密配置
5. 使用PyInstaller打包为单个可执行文件
6. 创建部署包和相关脚本
7. 清理临时文件

#### 2.3 构建输出
```
deploy/
├── ferry_system           # 加密的可执行文件
├── start.sh              # 启动脚本
├── ferry-system.service  # systemd服务文件
└── DEPLOY_README.md      # 部署说明(包含密钥)
```

### 第三步: 上传到Linux服务器

#### 3.1 上传部署包
```bash
# 方法1: 使用scp
scp -r deploy/ user@your-server:/tmp/ferry-deploy/

# 方法2: 使用rsync
rsync -av deploy/ user@your-server:/tmp/ferry-deploy/

# 方法3: 打包上传
tar czf ferry-deploy.tar.gz deploy/
scp ferry-deploy.tar.gz user@your-server:/tmp/
```

#### 3.2 在服务器上解压(如果使用方法3)
```bash
ssh user@your-server
cd /tmp
tar xzf ferry-deploy.tar.gz
```

### 第四步: 在Linux服务器上部署

#### 4.1 使用自动部署脚本(推荐)
```bash
# 上传部署脚本
scp deploy_to_linux.sh user@your-server:/tmp/

# 在服务器上运行
ssh user@your-server
sudo bash /tmp/deploy_to_linux.sh
```

#### 4.2 手动部署步骤
如果自动脚本失败，可以手动执行：

```bash
# 1. 创建应用用户
sudo useradd -r -s /bin/false ferry

# 2. 创建应用目录
sudo mkdir -p /opt/ferry-system/{data,logs}

# 3. 复制文件
sudo cp /tmp/ferry-deploy/ferry_system /opt/ferry-system/
sudo cp /tmp/ferry-deploy/start.sh /opt/ferry-system/
sudo chmod +x /opt/ferry-system/ferry_system
sudo chmod +x /opt/ferry-system/start.sh

# 4. 设置权限
sudo chown -R ferry:ferry /opt/ferry-system

# 5. 创建systemd服务
sudo cp /tmp/ferry-deploy/ferry-system.service /etc/systemd/system/
# 编辑服务文件，设置正确的加密密钥
sudo nano /etc/systemd/system/ferry-system.service

# 6. 启动服务
sudo systemctl daemon-reload
sudo systemctl enable ferry-system
sudo systemctl start ferry-system
```

### 第五步: 验证部署

#### 5.1 检查服务状态
```bash
# 查看服务状态
sudo systemctl status ferry-system

# 查看实时日志
sudo journalctl -u ferry-system -f

# 检查端口监听
sudo netstat -tlnp | grep 5000
```

#### 5.2 测试应用
```bash
# 健康检查
curl http://localhost:5000/api/health

# 查看API文档
curl http://localhost:5000/api/docs/

# 从外部访问(替换为实际IP)
curl http://your-server-ip:5000/api/health
```

## 🔧 配置管理

### 环境变量
应用需要以下环境变量：
```bash
# 加密密钥(必须)
export FERRY_ENCRYPTION_KEY="your-encryption-key-here"
```

### 服务配置
systemd服务文件位置: `/etc/systemd/system/ferry-system.service`

### 数据目录
- **数据库**: `/opt/ferry-system/data/ferry_system.db`
- **日志**: `/opt/ferry-system/logs/`
- **下载**: `/tmp/artifacts/` (可在配置中修改)

## 🛠️ 运维管理

### 常用命令
```bash
# 启动服务
sudo systemctl start ferry-system

# 停止服务
sudo systemctl stop ferry-system

# 重启服务
sudo systemctl restart ferry-system

# 查看状态
sudo systemctl status ferry-system

# 查看日志
sudo journalctl -u ferry-system -f

# 禁用服务
sudo systemctl disable ferry-system
```

### 日志管理
```bash
# 查看应用日志
sudo tail -f /opt/ferry-system/logs/sync.log
sudo tail -f /opt/ferry-system/logs/error.log

# 查看系统日志
sudo journalctl -u ferry-system --since "1 hour ago"
```

### 备份数据
```bash
# 备份数据库
sudo cp /opt/ferry-system/data/ferry_system.db /backup/ferry_system_$(date +%Y%m%d).db

# 备份整个数据目录
sudo tar czf /backup/ferry-data-$(date +%Y%m%d).tar.gz /opt/ferry-system/data/
```

## 🔒 安全注意事项

### 密钥管理
- ⚠️ **妥善保管加密密钥**，丢失后无法恢复
- 🔐 不要将密钥提交到版本控制系统
- 🔄 定期更换密钥(需要重新构建)

### 服务器安全
- 🔥 配置防火墙，只开放必要端口
- 👤 使用专用用户运行服务
- 📝 定期检查日志，监控异常访问
- 🔄 定期更新系统和依赖

### 网络安全
```bash
# 配置防火墙(CentOS/RHEL)
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# 配置防火墙(Ubuntu)
sudo ufw allow 5000/tcp
```

## 🚨 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 查看详细错误
sudo journalctl -u ferry-system -n 50

# 检查加密密钥
sudo systemctl show ferry-system -p Environment

# 手动测试
sudo -u ferry /opt/ferry-system/ferry_system
```

#### 2. 端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep 5000

# 修改端口(需要重新构建或修改配置)
```

#### 3. 权限问题
```bash
# 检查文件权限
ls -la /opt/ferry-system/

# 重新设置权限
sudo chown -R ferry:ferry /opt/ferry-system
sudo chmod +x /opt/ferry-system/ferry_system
```

#### 4. 数据库问题
```bash
# 检查数据库文件
ls -la /opt/ferry-system/data/

# 重新初始化数据库(会丢失数据)
sudo rm /opt/ferry-system/data/ferry_system.db
sudo systemctl restart ferry-system
```

## 📈 性能优化

### 系统资源
- **内存**: 建议至少512MB可用内存
- **磁盘**: 确保有足够空间存储制品和日志
- **网络**: 确保与制品仓库的网络连接稳定

### 应用优化
- 定期清理旧的同步记录
- 监控磁盘使用情况
- 配置日志轮转

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 操作系统版本
2. 错误日志内容
3. 服务状态信息
4. 网络环境描述

---

**注意**: 本部署方案适用于生产环境，请在测试环境充分验证后再部署到生产服务器。
